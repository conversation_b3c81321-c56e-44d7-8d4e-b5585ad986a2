// 文件: src/main/resources/rules/base/FieldStandardization.drl
// 功能: 字段标准化基础规则，包含通用字段处理函数
package rules.base;

import ai.pricefox.mallfox.utils.RuleLogUtil;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import java.util.Map;

/**
 * 规则名称：字段标准化规则(字段名称统一)
 * 功能描述：对动态标准产品对象的字段名称进行标准化处理
 * 优先级：100（最高优先级）
 * 触发条件：存在DynamicStandardProduct对象
 * 处理逻辑：
 *   1. 遍历所有字段，对字段名称进行标准化处理
 *   2. 处理带下划线的大写字段名（如"FIELD_NAME" -> "FieldName"）
 *   3. 处理全大写字段名（如"FIELDNAME" -> "Fieldname"）
 *   4. 处理首字母小写字段名（如"fieldname" -> "Fieldname"）
 *   5. 保留已标准化的字段名
 */
    rule "FieldStandardization.drl"
        no-loop true
        salience 100
        when
            $standardProduct: DynamicStandardProduct()
        then
            RuleLogUtil.info("进入 StandardizeFieldNamesRule 规则文件");
            Map<String, Object> fields = $standardProduct.getFields();
            Map<String, Object> normalizedFields = new java.util.HashMap<>();

            for (Map.Entry<String, Object> entry : fields.entrySet()) {
                String fieldName = entry.getKey();
                Object fieldValue = entry.getValue();

                // 处理带下划线的大写字段名
                if (fieldName.contains("_") && fieldName.equals(fieldName.toUpperCase())) {
                    StringBuilder sb = new StringBuilder();
                    String[] parts = fieldName.toLowerCase().split("_");
                    for (String part : parts) {
                        if (!part.isEmpty()) {
                            sb.append(Character.toUpperCase(part.charAt(0))).append(part.substring(1));
                        }
                    }
                    normalizedFields.put(sb.toString(), fieldValue);
                }
                // 处理全大写字段名
                else if (fieldName.equals(fieldName.toUpperCase()) && !fieldName.contains("_")) {
                    String normalizedUpper = Character.toUpperCase(fieldName.charAt(0)) + fieldName.substring(1).toLowerCase();
                    normalizedFields.put(normalizedUpper, fieldValue);
                }

                // 处理首字母小写字段名
                else if (Character.isLowerCase(fieldName.charAt(0))) {
                    String normalizedLower = Character.toUpperCase(fieldName.charAt(0)) + fieldName.substring(1);
                    normalizedFields.put(normalizedLower, fieldValue);
                }
                // 保留已标准化字段
                else {
                    normalizedFields.put(fieldName, fieldValue);
                }
            }

            fields.clear();
            fields.putAll(normalizedFields);
            RuleLogUtil.info("[字段标准化规则] 字段标准化完成，处理了" + normalizedFields.size()+ "个字段");
    end

    /**
     * 函数名称：isAllUppercase
     * 功能描述：判断字符串是否全部为大写字母
     * 输入参数：待判断的字符串
     * 返回值：布尔值
     */
    function boolean isAllUppercase(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        return str.equals(str.toUpperCase()) && !str.equals(str.toLowerCase());
    }

    /**
     * 函数名称：normalizeFieldName
     * 功能描述：对字段名称进行标准化处理
     * 输入参数：原始字段名
     * 返回值：标准化后的字段名
     * 处理逻辑：
     *   1. 处理带下划线的大写字段名（如"FIELD_NAME" -> "FieldName"）
     *   2. 处理全大写字段名（如"FIELDNAME" -> "Fieldname"）
     *   3. 处理首字母小写字段名（如"fieldname" -> "Fieldname"）
     */
    function String normalizeFieldName(String fieldName) {
        if (fieldName == null || fieldName.isEmpty()) {
            return fieldName;
        }

        if (fieldName.contains("_") && fieldName.equals(fieldName.toUpperCase())) {
            StringBuilder sb = new StringBuilder();
            String[] parts = fieldName.toLowerCase().split("_");
            for (String part : parts) {
                if (!part.isEmpty()) {
                    sb.append(Character.toUpperCase(part.charAt(0)))
                      .append(part.substring(1));
                }
            }
            return sb.toString();
        }

        if (fieldName.equals(fieldName.toUpperCase()) && !fieldName.contains("_")) {
            return Character.toUpperCase(fieldName.charAt(0)) + fieldName.substring(1).toLowerCase();
        }

        if (Character.isLowerCase(fieldName.charAt(0))) {
            return Character.toUpperCase(fieldName.charAt(0)) + fieldName.substring(1);
        }

        return fieldName;
    }

    // 辅助函数：首字母大写
    function String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return Character.toUpperCase(str.charAt(0)) + str.substring(1);
    }
