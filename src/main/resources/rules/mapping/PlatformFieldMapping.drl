// 文件: src/main/resources/rules/mapping/PlatformFieldMapping.drl
// 功能: 平台字段映射规则
package rules.mapping;

import ai.pricefox.mallfox.utils.RuleLogUtil;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.domain.standard.PlatformFieldMapping;
import ai.pricefox.mallfox.domain.standard.StandardField;
import java.util.Map;
import java.util.List;
import java.util.Map.Entry;

/**
 * 规则名称：平台字段映射规则
 * 功能描述：将平台特定字段映射到标准字段
 * 规则逻辑：
 *   1. 遍历原始数据中的源字段
 *   2. 查找源字段对应的平台字段映射
 *   3. 如果存在映射关系，使用标准字段code获取标准字段对象中的fieldNameEn
 *   4. 以fieldNameEn为key，源字段值为value存入标准产品对象的fields中
 */
rule "PlatformFieldMapping.drl"
    no-loop true
    salience 80
    when
        // 获取动态标准产品对象
        $standardProduct: DynamicStandardProduct()
        // 获取原始数据中的条目集合
        $entry: Map.Entry() from $standardProduct.getOriginData().entrySet()
    then
        // 从标准产品中获取平台字段映射列表
        List<PlatformFieldMapping> fieldMappingList = $standardProduct.getFieldMappingList();
        // 从标准产品中获取字段映射（标准字段code -> StandardField对象）
        Map<String,Object> fields = $standardProduct.getFields();
        
        // 获取源字段名和值
        String sourceFieldName = $entry.getKey().toString();
        Object value = $entry.getValue();
        
        // 查找源字段对应的平台字段映射
        PlatformFieldMapping mapping = null;
        for (Object obj : fieldMappingList) {
            if (!(obj instanceof PlatformFieldMapping)) {
                continue;
            }
            PlatformFieldMapping fieldMapping = (PlatformFieldMapping) obj;
            if (sourceFieldName.equals(fieldMapping.getSourceFieldName())) {
                mapping = fieldMapping;
                break;
            }
        }
        
        try {
            // 如果找到映射关系
            if (mapping != null) {
                // 获取标准字段编码
                String standardFieldCode = mapping.getStandardFieldCode();
                
                // 在字段映射中通过标准字段编码查找对应的StandardField对象
                if (fields.containsKey(standardFieldCode)) {
                    // 获取标准字段对象
                    Object fieldObj = fields.get(standardFieldCode);
                    // 确保对象是StandardField类型
                    if (fieldObj instanceof StandardField) {
                        // 类型转换为StandardField
                        StandardField standardField = (StandardField) fieldObj;
                        
                        // 获取标准字段的英文名称作为最终存储的键名
                        String fieldNameEn = standardField.getFieldNameEn();
                        
                        // 将原始数据中的值以标准字段英文名为键存入标准产品对象中
                        $standardProduct.getCurrentData().put(fieldNameEn, value);
                        
                        // 记录字段映射成功的日志
                        RuleLogUtil.info("[平台字段映射规则] 字段映射完成,第三方平台字段:'{}' -> 标准字段:'{}' = 值:'{}'", sourceFieldName, fieldNameEn, value.toString());
                    } else {
                        // 记录标准字段对象类型不匹配的警告日志，并包含源字段名称
                        RuleLogUtil.warn("[平台字段映射规则] 标准字段对象类型不匹配,第三方平台字段:'{}', 标准字段编码:'{}'", sourceFieldName, standardFieldCode);
                    }
                } else {
                    // 记录未找到标准字段对象的警告日志，并包含源字段名称
                    RuleLogUtil.warn("[平台字段映射规则] 未找到标准字段对象,第三方平台字段:'{}', 标准字段编码:'{}'", sourceFieldName, standardFieldCode);
                }
            }
            // 如果没有找到映射关系，记录未匹配的日志，并包含源字段名称
            else {
                RuleLogUtil.info("[平台字段映射规则] 源字段无法找到平台映射,第三方平台字段:'{}'", sourceFieldName);
            }
        } catch (Exception e) {
            // 记录处理字段映射时发生异常的错误日志
            RuleLogUtil.error("[平台字段映射规则] 处理字段映射时发生异常: {}", e.getMessage());
        }
end