// 文件: src/main/resources/rules/standardization/EntityStandardization.drl
// 功能: 实体字段标准化处理规则
package rules.standardization;

import ai.pricefox.mallfox.utils.RuleLogUtil;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.domain.standard.StandardBrand;
import ai.pricefox.mallfox.domain.standard.StandardColor;
import ai.pricefox.mallfox.domain.standard.StandardModel;
import ai.pricefox.mallfox.domain.standard.StandardStorage;
import ai.pricefox.mallfox.domain.standard.StandardCategory;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 规则名称：Process Entity Brand Standardization
 * 功能描述：实现品牌字段的多级标准化处理（实体专用）
 * 触发条件：
 *   1. 存在ProductDataDTO原始数据对象
 *   2. 存在DynamicStandardProduct标准产品对象
 *   3. 存在StandardBrand标准品牌库列表
 * 处理流程：
 *   1. 优先使用标准库精确匹配
 *   2. 其次从标题中提取品牌信息
 *   3. 最后使用原始品牌数据
 * 处理优先级：salience 100（最高优先级）
 */
rule "Process Entity Brand Standardization"
    no-loop true
    when
        // 匹配原始数据ProductDataDTO和标准产品对象
        $originalData: Map()
        $standardProduct: DynamicStandardProduct()
        // 获取所有标准品牌库
        $brands: List() from collect(StandardBrand())
    then {
        RuleLogUtil.info("进入 Process Entity Brand Standardization 规则文件");
        boolean processed = false;
        String brand = $standardProduct.getFields().get("Raw0000004") != null ? $standardProduct.getFields().get("Raw0000004").toString() : null;
        String standardizedBrand = null; // 声明标准化品牌变量

        // 第一阶段：标准库匹配
        if (brand != null && !brand.trim().isEmpty()) {
            String originalBrand = brand.trim();
            standardizedBrand = matchBrandWithStandardLibraryEntity(originalBrand, $brands);

            if (standardizedBrand != null) {
                $standardProduct.setField("Brand", standardizedBrand);
                RuleLogUtil.info("[品牌标准化规则] 品牌标准库匹配成功: 原始品牌='"+originalBrand+"' -> 标准品牌='"+standardizedBrand+"'");
                processed = true;
            }
        }

        // 第二阶段：标题提取
        String productTitle = $originalData.get("title") != null ? $originalData.get("title").toString() : "";
        if (productTitle != null && !processed) {
            productTitle = productTitle.toLowerCase();

            for (Object brandObj : $brands) {
                StandardBrand standardBrand = (StandardBrand) brandObj;
                String brandNameEn = standardBrand.getBrandNameEn();
                String brandNameCn = standardBrand.getBrandNameCn();

                if (brandNameEn != null && Pattern.compile("\\b" + Pattern.quote(brandNameEn.toLowerCase()) + "\\b").matcher(productTitle).find() || 
                    brandNameCn != null && productTitle.contains(brandNameCn.toLowerCase())) {
                    $standardProduct.setField("Brand", brandNameEn);
                    RuleLogUtil.info("[品牌标准化规则] 从商品标题中提取到品牌: 标题='"+productTitle+"' -> 匹配品牌='"+brandNameEn+"'");
                    processed = true;
                    break;
                }
            }

            // 特殊品牌识别逻辑
            if (!processed && (productTitle.contains("apple") && (productTitle.contains("iphone") || productTitle.contains("ipad") || productTitle.contains("mac")))) {
                $standardProduct.setField("Brand", "Apple");
                RuleLogUtil.info("[品牌标准化规则] 通过特殊逻辑识别到品牌: Apple");
                processed = true;
            } else if (!processed && productTitle.contains("samsung")) {
                $standardProduct.setField("Brand", "Samsung");
                RuleLogUtil.info("[品牌标准化规则] 通过特殊逻辑识别到品牌: Samsung");
                processed = true;
            } else if (!processed && productTitle.contains("google")) {
                $standardProduct.setField("Brand", "Google");
                RuleLogUtil.info("[品牌标准化规则] 通过特殊逻辑识别到品牌: Google");
                processed = true;
            }
        }

        // 第三阶段：原始数据兜底
        if (!processed && ($standardProduct.getStringField("Brand") == null || $standardProduct.getStringField("Brand").trim().isEmpty())
            && $originalData.containsKey("brand") && $originalData.get("brand") != null && !$originalData.get("brand").toString().trim().isEmpty()) {
            $standardProduct.setField("Brand", $originalData.get("brand").toString());
            RuleLogUtil.warn("[品牌标准化规则] 使用原始品牌数据作为兜底方案: 品牌='"+$originalData.get("brand")+"'");
        }
    }
end

/**
 * 功能描述：品牌标准库匹配函数（实体专用）
 * 输入参数：原始品牌值和标准品牌库列表
 * 返回值：匹配到的标准品牌名称
 * 处理逻辑：遍历标准品牌库进行精确匹配
 */
function String matchBrandWithStandardLibraryEntity(String originalValue, List brands) {
    if (originalValue == null || brands == null || brands.isEmpty()) {
        return null;
    }

    String trimmedValue = originalValue.trim();
    if (trimmedValue.isEmpty()) {
        return null;
    }

    for (Object brandObj : brands) {
        StandardBrand standardBrand = (StandardBrand) brandObj;
        String brandNameEn = standardBrand.getBrandNameEn();
        String brandNameCn = standardBrand.getBrandNameCn();

        if (trimmedValue.equalsIgnoreCase(brandNameEn) || trimmedValue.equalsIgnoreCase(brandNameCn)) {
            RuleLogUtil.debug("[品牌标准化规则] 标准库匹配函数找到匹配项: 原始值='"+trimmedValue+"', 标准品牌='"+brandNameEn+"'");
            return standardBrand.getBrandNameEn();
        }
    }
    return null;
}