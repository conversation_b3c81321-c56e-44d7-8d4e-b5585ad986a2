// 文件: src/main/resources/rules/standardization/StorageStandardization.drl
// 功能: 存储标准化处理规则
package rules.standardization;

import ai.pricefox.mallfox.utils.RuleLogUtil;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.domain.standard.StandardStorage;
import java.util.List;
import java.util.Map;

/**
 * 规则名称：Process Storage Standardization
 * 功能描述：实现存储字段的多级标准化处理
 * 触发条件：
 *   1. 存在原始数据Map对象
 *   2. 存在DynamicStandardProduct标准产品对象
 *   3. 存在StandardStorage标准存储库列表
 * 处理流程：
 *   1. 优先使用标准库精确匹配
 *   2. 其次从标题中提取存储信息
 *   3. 最后使用原始存储数据
 * 处理优先级：salience 100（最高优先级）
 */
rule "Process Storage Standardization"
    no-loop true
    when
        // 匹配原始数据Map和标准产品对象
        $originalData: Map()
        $standardProduct: DynamicStandardProduct()
        // 获取所有标准存储库
        $storages: List() from collect(StandardStorage())
    then {
        RuleLogUtil.info("进入 Process Storage Standardization 规则文件");
        boolean processed = false;
        String storage = null;
        
        // 安全获取字段值
        Object storageFieldValue = $standardProduct.getFields().get("Raw0000009");  // 重命名变量避免冲突
        if (storageFieldValue != null) {
            storage = storageFieldValue.toString();
        }
        
        // 第一阶段：标准库匹配
        if (storage != null && !storage.trim().isEmpty() && !processed) {
            String originalStorage = storage.trim();
            // 使用contains代替直接匹配
            for (Object storageIter : $storages) {
                StandardStorage standardStorage = (StandardStorage) storageIter;
                if (originalStorage.equalsIgnoreCase(standardStorage.getStorageName())) {
                    $standardProduct.setField("Storage", standardStorage.getStorageName());
                    RuleLogUtil.info("[存储标准化规则] 存储标准库匹配成功: 原始存储='{}' -> 标准存储='{}'", originalStorage, standardStorage.getStorageName());
                    processed = true;
                    break;
                }
            }
        }
        
        // 第二阶段：标题提取
        String title = (String)$originalData.get("title");
        if (title != null && !processed) {
            title = title.toLowerCase();

            for (Object storageObj : $storages) {
                StandardStorage standardStorage = (StandardStorage) storageObj;
                String storageName = standardStorage.getStorageName();

                if (storageName != null && title.contains(storageName.toLowerCase())) {
                    $standardProduct.setField("Storage", storageName);
                    RuleLogUtil.info("[存储标准化规则] 从商品标题中提取到存储信息: 标题='{}' -> 匹配存储='{}'", title, storageName);
                    processed = true;
                    break;
                }
            }
        }

        // 第三阶段：原始数据兜底
        if (!processed && ($standardProduct.getFields().get("Raw0000009").toString() == null)) {
            String rawStorage = (String)$originalData.get("storage");
            if (rawStorage != null && !rawStorage.trim().isEmpty()) {
                $standardProduct.setField("Storage", rawStorage);
                RuleLogUtil.warn("[存储标准化规则] 使用原始存储数据作为兜底方案: 存储='{}'", rawStorage);
            }
        }
    }
end