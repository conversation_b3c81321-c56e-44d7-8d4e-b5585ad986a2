// 文件: src/main/resources/rules/entity/CommonExtraction.drl
// 功能: 通用字段处理规则
package rules.entity;

import ai.pricefox.mallfox.utils.RuleLogUtil;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import java.util.Map;
import ai.pricefox.mallfox.service.rules.FieldMappingService;
import ai.pricefox.mallfox.domain.standard.StandardField;

/**
 * 规则名称：Process Common Fields
 * 功能描述：处理通用字段的标准化
 * 触发条件：
 *   1. 存在原始数据Map对象
 *   2. 存在DynamicStandardProduct标准产品对象
 * 处理流程：
 *   1. 通过字段映射服务获取标准字段映射
 *   2. 处理标题、UPC码、商品URL等通用字段
 * 处理优先级：salience 70
 */
rule "Process Common Fields"
    no-loop true
    salience 90
    when
        // 匹配原始数据Map和标准产品对象
        $standardProduct: DynamicStandardProduct()
        // 获取字段映射服务实例
        $fieldMappingService: FieldMappingService()
    then {
        RuleLogUtil.info("进入 StandardizeFieldNamesRule 规则文件");
        // 获取平台代码
        if ($standardProduct.getPlatformCode() == null) {
            RuleLogUtil.warn("[通用字段规则] 平台信息为空，无法进行字段映射");
        } else {
            // 获取平台字段映射关系
            Map<String, StandardField> fieldMapping = $fieldMappingService.getFieldMappingByPlatform($standardProduct.getPlatformCode());
            if (fieldMapping == null || fieldMapping.isEmpty()) {
                RuleLogUtil.warn("[通用字段规则] 未找到平台 "+$standardProduct.getPlatformName()+" 的字段映射关系");
            } else {
                // 获取标准字段名称
                StandardField titleField = fieldMapping.get("Raw0000005");
                String standardTitleField = titleField != null ? titleField.getFieldNameEn() : "Raw0000005";
                
                // 处理标题字段
                if ($standardProduct.getFields().containsKey("Raw0000005")) {
                    Object titleValue = $standardProduct.getFields().get("Raw0000005");
                    if (titleValue != null && !titleValue.toString().trim().isEmpty()) {
                        $standardProduct.setField(standardTitleField, titleValue.toString());
                        RuleLogUtil.info("[通用字段规则] 设置标题字段: "+standardTitleField+" = "+titleValue);
                    }
                }
                
                // 获取并处理UPC码字段
                StandardField upcField = fieldMapping.get("Raw0000007");
                String standardUpcField = upcField != null ? upcField.getFieldNameEn() : "Raw0000007";
                if ($standardProduct.getFields().containsKey("Raw0000007")) {
                    Object upcValue = $standardProduct.getFields().get("Raw0000007");
                    if (upcValue != null && !upcValue.toString().trim().isEmpty()) {
                        $standardProduct.setField(standardUpcField, upcValue.toString());
                        RuleLogUtil.info("[通用字段规则] 设置UPC码字段: "+standardUpcField+" = "+ upcValue);
                    }
                }
                
                // 获取并处理商品URL字段
                StandardField urlField = fieldMapping.get("Raw0000016");
                String standardUrlField = urlField != null ? urlField.getFieldNameEn() : "Raw0000016";
                if ($standardProduct.getFields().containsKey("Raw0000016")) {
                    Object urlValue = $standardProduct.getFields().get("Raw0000016");
                    if (urlValue != null && !urlValue.toString().trim().isEmpty()) {
                        $standardProduct.setField(standardUrlField, urlValue.toString());
                        RuleLogUtil.info("[通用字段规则] 设置商品URL字段: "+standardUrlField+" = "+urlValue);
                    }
                }
            }
        }
    }
end