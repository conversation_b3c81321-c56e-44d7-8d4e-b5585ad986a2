// 文件: src/main/resources/rules/normalization/Normalization.drl
package rules.normalization;

import ai.pricefox.mallfox.utils.RuleLogUtil;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.domain.standard.NormalizationLibrary;
import java.util.regex.Pattern;

/**
 * 规则名称：基于归一化库的动态归一化规则
 * 功能描述：使用归一化库进行动态数据归一化
 * 触发条件：
 *   1. 存在DynamicStandardProduct对象
 *   2. 存在NormalizationLibrary归一化规则
 * 处理流程：
 *   1. 使用归一化库匹配字段值
 *   2. 使用正则表达式进行模式匹配
 *   3. 应用匹配的归一化规则
 */
rule "Dynamic Normalization Using Library"
    no-loop true
    when
        $standardProduct: DynamicStandardProduct()
        $normLib: NormalizationLibrary(originalValue != null, normalizedValue != null)
    then
        RuleLogUtil.info("进入 Dynamic Normalization Using Library 规则文件");
        String originalValue = $normLib.getOriginalValue();
        String normalizedValue = $normLib.getNormalizedValue();


        if ($standardProduct.hasField("Brand")) {
            String brandValue = $standardProduct.getStringField("Brand").trim();
            String normBrand = originalValue.trim();

            RuleLogUtil.info("尝试匹配品牌: '" + brandValue + "' 与规则: '" + normBrand + "'");

            if (brandValue.equalsIgnoreCase(normBrand)) {
                $standardProduct.setField("Brand", normalizedValue);
                RuleLogUtil.info("Normalized brand: " + originalValue + " -> " + normalizedValue);
            } else if (Pattern.compile("[*+?{}()|\\[\\]\\\\]").matcher(normBrand).find()) {
                try {
                    if (Pattern.matches(normBrand, brandValue)) {
                        $standardProduct.setField("Brand", normalizedValue);
                        RuleLogUtil.info("Normalized brand: " + originalValue + " -> " + normalizedValue);
                    }
                } catch (Exception e) {
                    RuleLogUtil.warn("Invalid regex pattern for brand normalization: " + normBrand + ", error: " + e.getMessage());
                }
            }
        }
        else if ($standardProduct.hasField("Color")) {
            String colorValue = $standardProduct.getStringField("Color").trim();
            if (colorValue.equalsIgnoreCase(originalValue)) {
                $standardProduct.setField("Color", normalizedValue);
                RuleLogUtil.info("Normalized color: " + originalValue + " -> " + normalizedValue);
            }
        }
        else if ($standardProduct.hasField("Storage")) {
            String storageValue = $standardProduct.getStringField("Storage").trim();
            if (storageValue.equalsIgnoreCase(originalValue)) {
                $standardProduct.setField("Storage", normalizedValue);
                RuleLogUtil.info("Normalized storage: " + originalValue + " -> " + normalizedValue);
            }
        }
        else if ($standardProduct.hasField("Model")) {
            String modelValue = $standardProduct.getStringField("Model").trim();
            if (modelValue.equalsIgnoreCase(originalValue)) {
                $standardProduct.setField("Model", normalizedValue);
                RuleLogUtil.info("Normalized model: " + originalValue + " -> " + normalizedValue);
            }
        }
end
