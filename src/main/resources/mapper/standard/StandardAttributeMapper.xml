<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.standard.StandardAttributeMapper">

    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.standard.StandardAttribute">
            <id property="id" column="id" />
            <result property="createDate" column="create_date" />
            <result property="updateDate" column="update_date" />
            <result property="createUsername" column="create_username" />
            <result property="updateUsername" column="update_username" />
            <result property="attributeCode" column="attribute_code" />
            <result property="attributeNameCn" column="attribute_name_cn" />
            <result property="attributeNameEn" column="attribute_name_en" />
            <result property="standardCategoryCode" column="standard_category_code" />
            <result property="standardCategoryNameCn" column="standard_category_name_cn" />
            <result property="standardCategoryNameEn" column="standard_category_name_en" />
            <result property="deleted" column="deleted" />
            <result property="isSpec" column="is_spec" />
    </resultMap>

    <sql id="Base_Column_List">
        id,create_date,update_date,create_username,update_username,attribute_code,
        attribute_name_cn,attribute_name_en,standard_category_code,standard_category_name_cn,
        standard_category_name_en,deleted,is_spec
    </sql>
</mapper>
