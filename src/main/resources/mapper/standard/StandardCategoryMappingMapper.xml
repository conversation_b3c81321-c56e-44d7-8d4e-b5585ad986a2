<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.standard.StandardCategoryMappingMapper">

    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.standard.StandardCategoryMapping">
            <id property="id" column="id" />
            <result property="createDate" column="create_date" />
            <result property="updateDate" column="update_date" />
            <result property="createUsername" column="create_username" />
            <result property="updateUsername" column="update_username" />
            <result property="platformCode" column="platform_code" />
            <result property="standardCategoryCode" column="standard_category_code" />
            <result property="standardCategoryName" column="standard_category_name" />
            <result property="standardLevel" column="standard_level" />
            <result property="platformCategoryName" column="platform_category_name" />
            <result property="platformLevel" column="platform_level" />
    </resultMap>

    <sql id="Base_Column_List">
        id,create_date,update_date,create_username,update_username,platform_code,
        standard_category_code,standard_category_name,standard_level,platform_category_name,platform_level
    </sql>
</mapper>
