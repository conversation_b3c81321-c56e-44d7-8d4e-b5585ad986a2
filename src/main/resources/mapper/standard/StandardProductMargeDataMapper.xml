<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.standard.StandardProductMargeDataMapper">
  <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.standard.StandardProductMargeData">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_username" jdbcType="VARCHAR" property="createUsername" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_username" jdbcType="VARCHAR" property="updateUsername" />
    <result column="source_platform_name" jdbcType="VARCHAR" property="sourcePlatformName" />
    <result column="source_platform_code" jdbcType="VARCHAR" property="sourcePlatformCode" />
    <result column="data_channel" jdbcType="VARCHAR" property="dataChannel" />
    <result column="spu_code" jdbcType="VARCHAR" property="spuCode" />
    <result column="sku_code" jdbcType="VARCHAR" property="skuCode" />
    <result column="platform_spu_id" jdbcType="VARCHAR" property="platformSpuId" />
    <result column="platform_sku_id" jdbcType="VARCHAR" property="platformSkuId" />
    <result column="step" jdbcType="VARCHAR" property="step" />
    <result column="examine_status" jdbcType="VARCHAR" property="examineStatus" />
    <result column="category_level1" jdbcType="VARCHAR" property="categoryLevel1" />
    <result column="category_level2" jdbcType="VARCHAR" property="categoryLevel2" />
    <result column="category_level3" jdbcType="VARCHAR" property="categoryLevel3" />
    <result column="category_leve3_code" jdbcType="VARCHAR" property="categoryLeve3Code" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_date, create_username, update_date, update_username, source_platform_name, 
    source_platform_code, data_channel, spu_code, sku_code, platform_spu_id, platform_sku_id, 
    step, examine_status, category_level1, category_level2, category_level3, category_leve3_code
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from standard_product_marge_data
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from standard_product_marge_data
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="ai.pricefox.mallfox.domain.standard.StandardProductMargeData">
    insert into standard_product_marge_data (id, create_date, create_username, 
      update_date, update_username, source_platform_name, 
      source_platform_code, data_channel, spu_code, 
      sku_code, platform_spu_id, platform_sku_id, 
      step, examine_status, category_level1, 
      category_level2, category_level3, category_leve3_code
      )
    values (#{id,jdbcType=BIGINT}, #{createDate,jdbcType=TIMESTAMP}, #{createUsername,jdbcType=VARCHAR}, 
      #{updateDate,jdbcType=TIMESTAMP}, #{updateUsername,jdbcType=VARCHAR}, #{sourcePlatformName,jdbcType=VARCHAR}, 
      #{sourcePlatformCode,jdbcType=VARCHAR}, #{dataChannel,jdbcType=VARCHAR}, #{spuCode,jdbcType=VARCHAR}, 
      #{skuCode,jdbcType=VARCHAR}, #{platformSpuId,jdbcType=VARCHAR}, #{platformSkuId,jdbcType=VARCHAR}, 
      #{step,jdbcType=VARCHAR}, #{examineStatus,jdbcType=VARCHAR}, #{categoryLevel1,jdbcType=VARCHAR}, 
      #{categoryLevel2,jdbcType=VARCHAR}, #{categoryLevel3,jdbcType=VARCHAR}, #{categoryLeve3Code,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="ai.pricefox.mallfox.domain.standard.StandardProductMargeData">
    insert into standard_product_marge_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="updateDate != null">
        update_date,
      </if>
      <if test="updateUsername != null">
        update_username,
      </if>
      <if test="sourcePlatformName != null">
        source_platform_name,
      </if>
      <if test="sourcePlatformCode != null">
        source_platform_code,
      </if>
      <if test="dataChannel != null">
        data_channel,
      </if>
      <if test="spuCode != null">
        spu_code,
      </if>
      <if test="skuCode != null">
        sku_code,
      </if>
      <if test="platformSpuId != null">
        platform_spu_id,
      </if>
      <if test="platformSkuId != null">
        platform_sku_id,
      </if>
      <if test="step != null">
        step,
      </if>
      <if test="examineStatus != null">
        examine_status,
      </if>
      <if test="categoryLevel1 != null">
        category_level1,
      </if>
      <if test="categoryLevel2 != null">
        category_level2,
      </if>
      <if test="categoryLevel3 != null">
        category_level3,
      </if>
      <if test="categoryLeve3Code != null">
        category_leve3_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null">
        #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUsername != null">
        #{updateUsername,jdbcType=VARCHAR},
      </if>
      <if test="sourcePlatformName != null">
        #{sourcePlatformName,jdbcType=VARCHAR},
      </if>
      <if test="sourcePlatformCode != null">
        #{sourcePlatformCode,jdbcType=VARCHAR},
      </if>
      <if test="dataChannel != null">
        #{dataChannel,jdbcType=VARCHAR},
      </if>
      <if test="spuCode != null">
        #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="skuCode != null">
        #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="platformSpuId != null">
        #{platformSpuId,jdbcType=VARCHAR},
      </if>
      <if test="platformSkuId != null">
        #{platformSkuId,jdbcType=VARCHAR},
      </if>
      <if test="step != null">
        #{step,jdbcType=VARCHAR},
      </if>
      <if test="examineStatus != null">
        #{examineStatus,jdbcType=VARCHAR},
      </if>
      <if test="categoryLevel1 != null">
        #{categoryLevel1,jdbcType=VARCHAR},
      </if>
      <if test="categoryLevel2 != null">
        #{categoryLevel2,jdbcType=VARCHAR},
      </if>
      <if test="categoryLevel3 != null">
        #{categoryLevel3,jdbcType=VARCHAR},
      </if>
      <if test="categoryLeve3Code != null">
        #{categoryLeve3Code,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="ai.pricefox.mallfox.domain.standard.StandardProductMargeData">
    update standard_product_marge_data
    <set>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        create_username = #{createUsername,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null">
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUsername != null">
        update_username = #{updateUsername,jdbcType=VARCHAR},
      </if>
      <if test="sourcePlatformName != null">
        source_platform_name = #{sourcePlatformName,jdbcType=VARCHAR},
      </if>
      <if test="sourcePlatformCode != null">
        source_platform_code = #{sourcePlatformCode,jdbcType=VARCHAR},
      </if>
      <if test="dataChannel != null">
        data_channel = #{dataChannel,jdbcType=VARCHAR},
      </if>
      <if test="spuCode != null">
        spu_code = #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="skuCode != null">
        sku_code = #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="platformSpuId != null">
        platform_spu_id = #{platformSpuId,jdbcType=VARCHAR},
      </if>
      <if test="platformSkuId != null">
        platform_sku_id = #{platformSkuId,jdbcType=VARCHAR},
      </if>
      <if test="step != null">
        step = #{step,jdbcType=VARCHAR},
      </if>
      <if test="examineStatus != null">
        examine_status = #{examineStatus,jdbcType=VARCHAR},
      </if>
      <if test="categoryLevel1 != null">
        category_level1 = #{categoryLevel1,jdbcType=VARCHAR},
      </if>
      <if test="categoryLevel2 != null">
        category_level2 = #{categoryLevel2,jdbcType=VARCHAR},
      </if>
      <if test="categoryLevel3 != null">
        category_level3 = #{categoryLevel3,jdbcType=VARCHAR},
      </if>
      <if test="categoryLeve3Code != null">
        category_leve3_code = #{categoryLeve3Code,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="ai.pricefox.mallfox.domain.standard.StandardProductMargeData">
    update standard_product_marge_data
    set create_date = #{createDate,jdbcType=TIMESTAMP},
      create_username = #{createUsername,jdbcType=VARCHAR},
      update_date = #{updateDate,jdbcType=TIMESTAMP},
      update_username = #{updateUsername,jdbcType=VARCHAR},
      source_platform_name = #{sourcePlatformName,jdbcType=VARCHAR},
      source_platform_code = #{sourcePlatformCode,jdbcType=VARCHAR},
      data_channel = #{dataChannel,jdbcType=VARCHAR},
      spu_code = #{spuCode,jdbcType=VARCHAR},
      sku_code = #{skuCode,jdbcType=VARCHAR},
      platform_spu_id = #{platformSpuId,jdbcType=VARCHAR},
      platform_sku_id = #{platformSkuId,jdbcType=VARCHAR},
      step = #{step,jdbcType=VARCHAR},
      examine_status = #{examineStatus,jdbcType=VARCHAR},
      category_level1 = #{categoryLevel1,jdbcType=VARCHAR},
      category_level2 = #{categoryLevel2,jdbcType=VARCHAR},
      category_level3 = #{categoryLevel3,jdbcType=VARCHAR},
      category_leve3_code = #{categoryLeve3Code,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>