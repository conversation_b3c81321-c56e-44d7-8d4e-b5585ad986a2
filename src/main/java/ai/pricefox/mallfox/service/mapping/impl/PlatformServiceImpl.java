package ai.pricefox.mallfox.service.mapping.impl;

import ai.pricefox.mallfox.common.exception.enums.ErrorCodeConstants;
import ai.pricefox.mallfox.enums.ProductPlatformEnum;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.domain.standard.TripartitePlatform;
import ai.pricefox.mallfox.mapper.standard.TripartitePlatformMapper;
import ai.pricefox.mallfox.model.vo.mapping.platform.*;
import ai.pricefox.mallfox.service.mapping.PlatformService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

import static ai.pricefox.mallfox.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 平台管理Service实现类
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Slf4j
@Service
@AllArgsConstructor
public class PlatformServiceImpl extends ServiceImpl<TripartitePlatformMapper, TripartitePlatform> implements PlatformService {

    private final TripartitePlatformMapper tripartitePlatformMapper;

    @Override
    public CommonResult<List<PlatformRespVO>> getPlatformList() {
        List<TripartitePlatform> platforms = tripartitePlatformMapper.selectList(
                new LambdaQueryWrapper<TripartitePlatform>()
                        .orderByDesc(TripartitePlatform::getCreateDate)
        );

        List<PlatformRespVO> respVOList = platforms.stream()
                .map(this::convertToRespVO)
                .collect(Collectors.toList());

        return CommonResult.success(respVOList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<PlatformRespVO> createPlatform(PlatformCreateReqVO reqVO) {
        // 检查平台名称是否已存在
        LambdaQueryWrapper<TripartitePlatform> nameWrapper = new LambdaQueryWrapper<>();
        nameWrapper.eq(TripartitePlatform::getPlatformName, reqVO.getPlatformName());
        TripartitePlatform existingByName = tripartitePlatformMapper.selectOne(nameWrapper);
        if (existingByName != null) {
            throw exception(ErrorCodeConstants.PLATFORM_NAME_EXISTS);
        }

        // 检查平台中文名称是否已存在
        LambdaQueryWrapper<TripartitePlatform> cnNameWrapper = new LambdaQueryWrapper<>();
        cnNameWrapper.eq(TripartitePlatform::getPlatformNameCn, reqVO.getPlatformNameCn());
        TripartitePlatform existingByCnName = tripartitePlatformMapper.selectOne(cnNameWrapper);
        if (existingByCnName != null) {
            throw exception(ErrorCodeConstants.PLATFORM_CN_NAME_EXISTS);
        }

        // 创建平台
        TripartitePlatform platform = new TripartitePlatform();
        BeanUtils.copyProperties(reqVO, platform);
        
        // 自动生成平台编码
        platform.setPlatformCode(generatePlatformCode());
        platform.setCreateDate(new Date());
        platform.setCreateUsername("system");

        tripartitePlatformMapper.insert(platform);

        PlatformRespVO respVO = convertToRespVO(platform);
        return CommonResult.success(respVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<PlatformRespVO> updatePlatform(PlatformUpdateReqVO reqVO) {
        // 检查平台是否存在
        TripartitePlatform existingPlatform = tripartitePlatformMapper.selectById(reqVO.getId());
        if (existingPlatform == null) {
            throw exception(ErrorCodeConstants.PLATFORM_NOT_EXIST);
        }

        // 检查平台名称是否被其他平台使用
        LambdaQueryWrapper<TripartitePlatform> nameWrapper = new LambdaQueryWrapper<>();
        nameWrapper.ne(TripartitePlatform::getId, reqVO.getId())
                .eq(TripartitePlatform::getPlatformName, reqVO.getPlatformName());
        TripartitePlatform platformWithSameName = tripartitePlatformMapper.selectOne(nameWrapper);
        if (platformWithSameName != null) {
            throw exception(ErrorCodeConstants.PLATFORM_NAME_EXISTS);
        }

        // 检查平台中文名称是否被其他平台使用
        LambdaQueryWrapper<TripartitePlatform> cnNameWrapper = new LambdaQueryWrapper<>();
        cnNameWrapper.ne(TripartitePlatform::getId, reqVO.getId())
                .eq(TripartitePlatform::getPlatformNameCn, reqVO.getPlatformNameCn());
        TripartitePlatform platformWithSameCnName = tripartitePlatformMapper.selectOne(cnNameWrapper);
        if (platformWithSameCnName != null) {
            throw exception(ErrorCodeConstants.PLATFORM_CN_NAME_EXISTS);
        }

        // 更新平台信息（平台编码不允许修改）
        existingPlatform.setPlatformName(ProductPlatformEnum.valueOf(reqVO.getPlatformName()));
        existingPlatform.setPlatformNameCn(reqVO.getPlatformNameCn());
        existingPlatform.setEnable(reqVO.getEnable());
        existingPlatform.setUpdateDate(new Date());
        existingPlatform.setUpdateUsername("system");

        tripartitePlatformMapper.updateById(existingPlatform);

        PlatformRespVO respVO = convertToRespVO(existingPlatform);
        return CommonResult.success(respVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> deletePlatform(Integer id) {
        // 检查平台是否存在
        TripartitePlatform platform = tripartitePlatformMapper.selectById(id);
        if (platform == null) {
            throw exception(ErrorCodeConstants.PLATFORM_NOT_EXIST);
        }

        // TODO: 检查是否有关联的映射数据，如果有则不允许删除
        // 可以在后续添加相关检查逻辑

        // 删除平台
        int result = tripartitePlatformMapper.deleteById(id);
        return CommonResult.success(result > 0);
    }

    @Override
    public CommonResult<PlatformRespVO> getPlatformById(Integer id) {
        TripartitePlatform platform = tripartitePlatformMapper.selectById(id);
        if (platform == null) {
            throw exception(ErrorCodeConstants.PLATFORM_NOT_EXIST);
        }

        PlatformRespVO respVO = convertToRespVO(platform);
        return CommonResult.success(respVO);
    }

    /**
     * 生成平台编码：ECOM + 8位数字
     */
    private String generatePlatformCode() {
        String code;
        int maxAttempts = 10;
        int attempts = 0;
        
        do {
            // 生成8位随机数字
            Random random = new Random();
            int randomNumber = 10000000 + random.nextInt(90000000);
            code = "ECOM" + randomNumber;
            
            // 检查编码是否已存在
            LambdaQueryWrapper<TripartitePlatform> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TripartitePlatform::getPlatformCode, code);
            TripartitePlatform existing = tripartitePlatformMapper.selectOne(wrapper);
            
            if (existing == null) {
                return code;
            }
            
            attempts++;
        } while (attempts < maxAttempts);
        
        // 如果10次都重复，使用时间戳确保唯一性
        long timestamp = System.currentTimeMillis();
        return "ECOM" + String.valueOf(timestamp).substring(5);
    }

    /**
     * 转换为响应VO
     */
    private PlatformRespVO convertToRespVO(TripartitePlatform platform) {
        PlatformRespVO respVO = new PlatformRespVO();
        BeanUtils.copyProperties(platform, respVO);
        return respVO;
    }
}