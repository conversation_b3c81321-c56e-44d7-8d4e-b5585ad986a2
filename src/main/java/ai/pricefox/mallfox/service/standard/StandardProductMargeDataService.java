package ai.pricefox.mallfox.service.standard;

import ai.pricefox.mallfox.domain.standard.StandardProductMargeData;
import ai.pricefox.mallfox.model.param.ProductDataSearchRequest;
import ai.pricefox.mallfox.vo.product.HeaderInfoVO;
import ai.pricefox.mallfox.vo.product.ProductAttributeVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

public interface StandardProductMargeDataService {

    /**
     * 获取商品数据表头信息
     * 
     * @return 表头信息列表
     */
    List<HeaderInfoVO> getProductHeaders();

}
