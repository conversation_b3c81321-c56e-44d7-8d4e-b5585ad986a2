package ai.pricefox.mallfox.service.standard.impl;

import ai.pricefox.mallfox.common.exception.enums.ErrorCodeConstants;
import ai.pricefox.mallfox.common.util.IdGenerator;
import ai.pricefox.mallfox.domain.standard.StandardCategory;
import ai.pricefox.mallfox.mapper.standard.StandardAttributeMapper;
import ai.pricefox.mallfox.mapper.standard.StandardAttributeMappingMapper;
import ai.pricefox.mallfox.mapper.standard.StandardAttributeValueMapper;
import ai.pricefox.mallfox.mapper.standard.StandardAttributeValueMappingMapper;
import ai.pricefox.mallfox.mapper.standard.StandardCategoryMapper;
import ai.pricefox.mallfox.mapper.standard.StandardCategoryMappingMapper;
import ai.pricefox.mallfox.service.standard.StandardCategoryService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.base.PageResult;
import ai.pricefox.mallfox.vo.category.*;
import ai.pricefox.mallfox.model.excel.CategoryExportExcelEntity;
import com.alibaba.excel.EasyExcel;
import lombok.extern.slf4j.Slf4j;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import static ai.pricefox.mallfox.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 标准品类库Service实现类
 * <AUTHOR>
 * @date 2025-07-24
 */
@Slf4j
@Service
@AllArgsConstructor
public class StandardCategoryServiceImpl extends ServiceImpl<StandardCategoryMapper, StandardCategory> implements StandardCategoryService {

    private final StandardCategoryMapper standardCategoryMapper;
    private final StandardCategoryMappingMapper standardCategoryMappingMapper;
    private final StandardAttributeMapper standardAttributeMapper;
    private final StandardAttributeMappingMapper standardAttributeMappingMapper;
    private final StandardAttributeValueMapper standardAttributeValueMapper;
    private final StandardAttributeValueMappingMapper standardAttributeValueMappingMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<CategoryDetailRespVO> createCategory(CategoryCreateReqVO reqVO) {
        // 1. 校验英文名称重复（调用Mapper方法）
        StandardCategory existingByName = standardCategoryMapper.findByCategoryNameEn(reqVO.getCategoryNameEn());
        if (existingByName != null) {
            throw exception(ErrorCodeConstants.CATEGORY_INFO_NAME_EXISTS);
        }

        // 2. 生成编码并检查重复
        String categoryCode;
        StandardCategory existingByCode;
        do {
            categoryCode = IdGenerator.generateCategoryCode();
            existingByCode = standardCategoryMapper.findByCategoryCode(categoryCode);
        } while (existingByCode != null);

        // 3. 验证父分类存在性并计算层级
        Integer level = 1;
        if (reqVO.getParent() != null && reqVO.getParent() > 0) {
            StandardCategory parentCategory = standardCategoryMapper.selectById(reqVO.getParent());
            if (parentCategory == null) {
                throw exception(ErrorCodeConstants.CATEGORY_INFO_PARENT_NOT_EXIST);
            }
            level = parentCategory.getLevel() + 1;
        }

        // 4. 构建实体并保存
        StandardCategory category = buildCategoryEntity(reqVO, categoryCode, level);
        standardCategoryMapper.insert(category);

        return CommonResult.success(convertToDetailRespVO(category));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<CategoryDetailRespVO> updateCategory(CategoryUpdateReqVO reqVO) {
        // 1. 检查分类是否存在
        StandardCategory existingCategory = standardCategoryMapper.selectById(reqVO.getId());
        if (existingCategory == null) {
            throw exception(ErrorCodeConstants.CATEGORY_INFO_NOT_EXIST);
        }

        // 2. 检查英文名称是否重复（排除自己）
        StandardCategory categoryWithSameName = standardCategoryMapper.findByCategoryNameEn(reqVO.getCategoryNameEn());
        if (categoryWithSameName != null && !categoryWithSameName.getId().equals(reqVO.getId())) {
            throw exception(ErrorCodeConstants.CATEGORY_INFO_NAME_EXISTS);
        }

        // 3. 验证父分类存在性（不能设置自己为父分类）
        Integer level = existingCategory.getLevel();
        if (reqVO.getParent() != null && reqVO.getParent() > 0) {
            if (reqVO.getParent().equals(reqVO.getId())) {
                throw exception(ErrorCodeConstants.CATEGORY_INFO_PARENT_ERROR);
            }
            StandardCategory parentCategory = standardCategoryMapper.selectById(reqVO.getParent());
            if (parentCategory == null) {
                throw exception(ErrorCodeConstants.CATEGORY_INFO_PARENT_NOT_EXIST);
            }
            level = parentCategory.getLevel() + 1;
        } else {
            level = 1;
        }

        // 4. 更新分类信息
        existingCategory.setCategoryNameEn(reqVO.getCategoryNameEn());
        existingCategory.setCategoryNameCn(reqVO.getCategoryNameCn());
        existingCategory.setLevel(level);
        existingCategory.setIconUrl(reqVO.getIconUrl());
        existingCategory.setIsActive(reqVO.getIsActive() != null ? reqVO.getIsActive() : existingCategory.getIsActive());
        existingCategory.setParent(reqVO.getParent() != null ? reqVO.getParent() : 0L);
        existingCategory.setUpdateDate(LocalDateTime.now());
        existingCategory.setUpdateUsername("system");

        standardCategoryMapper.updateById(existingCategory);

        return CommonResult.success(convertToDetailRespVO(existingCategory));
    }

    @Override
    public CommonResult<CategoryDetailRespVO> getCategoryById(Long id) {
        StandardCategory standardCategory = standardCategoryMapper.selectById(id);
        if (standardCategory == null) {
            throw exception(ErrorCodeConstants.CATEGORY_INFO_NOT_EXIST);
        }

        return CommonResult.success(convertToDetailRespVO(standardCategory));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> deleteCategory(Long id) {
        // 1. 检查分类是否存在
        StandardCategory category = standardCategoryMapper.selectById(id);
        if (category == null) {
            throw exception(ErrorCodeConstants.CATEGORY_INFO_NOT_EXIST);
        }

        // 2. 检查是否有子分类（调用Mapper方法）
        long childCount = standardCategoryMapper.countChildrenByParentId(id);
        if (childCount > 0) {
            throw exception(ErrorCodeConstants.CATEGORY_INFO_HAS_CHILDREN);
        }

        // 3. 检查是否有关联的属性（调用Mapper方法）
        long attrCount = standardAttributeMapper.countByCategoryCode(category.getCategoryCode());
        if (attrCount > 0) {
            throw exception(ErrorCodeConstants.CATEGORY_HAS_ATTRIBUTES);
        }

        // 4. 检查是否有平台映射关系（调用Mapper方法）
        long mappingCount = standardCategoryMappingMapper.countByStandardCategoryCode(category.getCategoryCode());
        if (mappingCount > 0) {
            throw exception(ErrorCodeConstants.CATEGORY_HAS_PLATFORM_MAPPING);
        }

        // 5. 所有检查通过，执行删除（只删除分类本身）
        int result = standardCategoryMapper.deleteById(id);

        return CommonResult.success(result > 0);
    }

    @Override
    public CommonResult<List<CategoryTreeRespVO>> getCategoryTree(CategoryListReqVO reqVO) {
        // 调用Mapper方法获取数据
        List<StandardCategory> categoryList = standardCategoryMapper.selectTreeList(
                reqVO.getCategoryCode(), reqVO.getCategoryNameEn(), reqVO.getCategoryNameCn());

        // 构建树形结构
        List<CategoryTreeRespVO> treeList = buildCategoryTree(categoryList, 0L);

        return CommonResult.success(treeList);
    }

    @Override
    public CommonResult<PageResult<CategoryDetailRespVO>> getCategoryList(CategoryListReqVO reqVO) {
        // 调用Mapper方法进行分页查询
        Page<StandardCategory> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        Page<StandardCategory> result = (Page<StandardCategory>) standardCategoryMapper.selectPageList(
                page, reqVO.getCategoryCode(), reqVO.getCategoryNameEn(), reqVO.getCategoryNameCn());

        List<CategoryDetailRespVO> respVOList = result.getRecords().stream()
                .map(this::convertToDetailRespVO)
                .collect(Collectors.toList());

        PageResult<CategoryDetailRespVO> pageResult = new PageResult<>(respVOList, result.getTotal());
        return CommonResult.success(pageResult);
    }

    @Override
    public CommonResult<String> exportCategories(CategoryListReqVO reqVO) {
        try {
            // 获取所有符合条件的分类数据（不分页）
            List<StandardCategory> categoryList = standardCategoryMapper.selectTreeList(
                    reqVO.getCategoryCode(), reqVO.getCategoryNameEn(), reqVO.getCategoryNameCn());

            // 转换为导出实体
            List<CategoryExportExcelEntity> exportList = categoryList.stream()
                    .map(this::convertToExportEntity)
                    .collect(Collectors.toList());

            // 生成文件路径
            String fileName = "categories_export_" + System.currentTimeMillis() + ".xlsx";
            String filePath = "/tmp/" + fileName;

            // 使用EasyExcel导出
            EasyExcel.write(filePath, CategoryExportExcelEntity.class)
                    .sheet("分类数据")
                    .doWrite(exportList);

            return CommonResult.success(filePath);
        } catch (Exception e) {
            log.error("导出分类数据失败", e);
            return CommonResult.error(500, "导出失败: " + e.getMessage());
        }
    }

    /**
     * 构建分类实体
     */
    private StandardCategory buildCategoryEntity(CategoryCreateReqVO reqVO, String categoryCode, Integer level) {
        StandardCategory category = new StandardCategory();
        category.setCategoryCode(categoryCode);
        category.setCategoryNameEn(reqVO.getCategoryNameEn());
        category.setCategoryNameCn(reqVO.getCategoryNameCn());
        category.setLevel(level);
        category.setIconUrl(reqVO.getIconUrl());
        category.setSort(1); // 默认排序
        category.setIsActive(true); // 默认激活
        category.setParent(reqVO.getParent() != null ? reqVO.getParent() : 0L);
        category.setCreateDate(LocalDateTime.now());
        category.setCreateUsername("system");
        return category;
    }

    /**
     * 构建分类树形结构
     */
    private List<CategoryTreeRespVO> buildCategoryTree(List<StandardCategory> categoryList, Long parentId) {
        return categoryList.stream()
                .filter(category -> category.getParent().equals(parentId))
                .sorted((a, b) -> {
                    // 按英文名称首字母排序
                    String firstLetterA = getFirstLetter(a.getCategoryNameEn());
                    String firstLetterB = getFirstLetter(b.getCategoryNameEn());
                    return firstLetterA.compareTo(firstLetterB);
                })
                .map(category -> {
                    CategoryTreeRespVO treeVO = convertToTreeRespVO(category);
                    List<CategoryTreeRespVO> children = buildCategoryTree(categoryList, category.getId());
                    treeVO.setChildren(children);
                    return treeVO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取首字母（处理非字母字符）
     */
    private String getFirstLetter(String name) {
        if (name == null || name.isEmpty()) {
            return "z"; // 默认排在最后
        }
        for (char c : name.toCharArray()) {
            if (Character.isLetter(c)) {
                return String.valueOf(Character.toLowerCase(c));
            }
        }
        return "z"; // 默认排在最后
    }

    /**
     * 转换为树形响应VO
     */
    private CategoryTreeRespVO convertToTreeRespVO(StandardCategory category) {
        CategoryTreeRespVO respVO = new CategoryTreeRespVO();
        respVO.setId(category.getId());
        respVO.setCategoryCode(category.getCategoryCode());
        respVO.setCategoryNameEn(category.getCategoryNameEn());
        respVO.setCategoryNameCn(category.getCategoryNameCn());
        respVO.setLevel(category.getLevel());
        respVO.setIconUrl(category.getIconUrl());
        respVO.setSort(category.getSort());
        respVO.setIsActive(category.getIsActive());
        respVO.setParent(category.getParent());
        respVO.setCreateDate(category.getCreateDate());
        respVO.setUpdateDate(category.getUpdateDate());
        return respVO;
    }

    /**
     * 转换为详情响应VO
     */
    private CategoryDetailRespVO convertToDetailRespVO(StandardCategory category) {
        CategoryDetailRespVO respVO = new CategoryDetailRespVO();
        respVO.setId(category.getId());
        respVO.setCategoryCode(category.getCategoryCode());
        respVO.setCategoryNameEn(category.getCategoryNameEn());
        respVO.setCategoryNameCn(category.getCategoryNameCn());
        respVO.setLevel(category.getLevel());
        respVO.setIconUrl(category.getIconUrl());
        respVO.setSort(category.getSort());
        respVO.setIsActive(category.getIsActive());
        respVO.setParent(category.getParent());
        respVO.setCreateDate(category.getCreateDate());
        respVO.setUpdateDate(category.getUpdateDate());
        respVO.setCreateUsername(category.getCreateUsername());
        respVO.setUpdateUsername(category.getUpdateUsername());
        return respVO;
    }

    /**
     * 转换为导出实体
     */
    private CategoryExportExcelEntity convertToExportEntity(StandardCategory category) {
        CategoryExportExcelEntity exportEntity = new CategoryExportExcelEntity();
        exportEntity.setId(category.getId());
        exportEntity.setCategoryCode(category.getCategoryCode());
        exportEntity.setCategoryNameEn(category.getCategoryNameEn());
        exportEntity.setCategoryNameCn(category.getCategoryNameCn());
        exportEntity.setLevel(category.getLevel());
        exportEntity.setIconUrl(category.getIconUrl());
        exportEntity.setSort(category.getSort());
        exportEntity.setIsActiveText(category.getIsActive() != null && category.getIsActive() ? "是" : "否");
        exportEntity.setParent(category.getParent());
        exportEntity.setCreateDate(category.getCreateDate());
        exportEntity.setUpdateDate(category.getUpdateDate());
        exportEntity.setCreateUsername(category.getCreateUsername());
        exportEntity.setUpdateUsername(category.getUpdateUsername());
        return exportEntity;
    }
}