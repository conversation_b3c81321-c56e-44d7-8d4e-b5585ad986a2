package ai.pricefox.mallfox.service.standard.impl;

import ai.pricefox.mallfox.domain.standard.StandardProductMargeData;
import ai.pricefox.mallfox.mapper.standard.StandardProductMargeDataMapper;
import ai.pricefox.mallfox.service.standard.StandardProductMargeDataService;
import ai.pricefox.mallfox.vo.product.HeaderInfoVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> @description 服务实现类
 * @date 2025-08-01 13:35:03
 */
@Service
public class StandardProductMargeDataServiceImpl extends ServiceImpl<StandardProductMargeDataMapper, StandardProductMargeData> implements StandardProductMargeDataService {

    @Override
    public List<HeaderInfoVO> getProductHeaders() {
        // TODO: 实际实现时需要根据数据库表结构动态生成表头信息
        return new ArrayList<>();
    }
}