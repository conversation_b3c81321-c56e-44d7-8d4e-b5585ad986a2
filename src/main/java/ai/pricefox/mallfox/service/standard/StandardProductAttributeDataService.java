package ai.pricefox.mallfox.service.standard;

import ai.pricefox.mallfox.domain.standard.StandardProductAttributeData;
import ai.pricefox.mallfox.model.param.ProductDataSearchRequest;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;

/**
 * <AUTHOR> @description 针对表【standard_product_attribute_data】的业务逻辑
 * @date 2025-08-01 13:35:03
 */
public interface StandardProductAttributeDataService {
    /**
     * 根据skuCode查询属性数据
     *
     * @param skuCode 商品SKU编码
     * @return 属性数据列表
     */
    List<StandardProductAttributeData> getBySkuCode(String skuCode);
    
    /**
     * 批量查询SKU属性数据
     * @param skuCodes SKU编码列表
     * @return 属性数据列表
     */
    List<StandardProductAttributeData> getBySkuCodes(List<String> skuCodes);
}