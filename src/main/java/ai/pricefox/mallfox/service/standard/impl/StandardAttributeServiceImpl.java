package ai.pricefox.mallfox.service.standard.impl;

import ai.pricefox.mallfox.domain.standard.StandardAttribute;
import ai.pricefox.mallfox.mapper.standard.StandardAttributeMapper;
import ai.pricefox.mallfox.service.standard.StandardAttributeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 标准属性库Service实现类
 * <AUTHOR>
 * @date 2025-08-02
 */
@Service
@AllArgsConstructor
public class StandardAttributeServiceImpl extends ServiceImpl<StandardAttributeMapper, StandardAttribute> implements StandardAttributeService {

    private final StandardAttributeMapper standardAttributeMapper;

}