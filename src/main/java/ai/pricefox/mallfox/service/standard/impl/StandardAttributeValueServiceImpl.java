package ai.pricefox.mallfox.service.standard.impl;

import ai.pricefox.mallfox.domain.standard.StandardAttributeValue;
import ai.pricefox.mallfox.mapper.standard.StandardAttributeValueMapper;
import ai.pricefox.mallfox.service.standard.StandardAttributeValueService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 标准属性值Service实现类
 * <AUTHOR>
 * @date 2025-08-02
 */
@Service
@AllArgsConstructor
public class StandardAttributeValueServiceImpl extends ServiceImpl<StandardAttributeValueMapper, StandardAttributeValue> implements StandardAttributeValueService {

    private final StandardAttributeValueMapper standardAttributeValueMapper;

}