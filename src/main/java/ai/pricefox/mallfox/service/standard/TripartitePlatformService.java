package ai.pricefox.mallfox.service.standard;

import ai.pricefox.mallfox.domain.standard.TripartitePlatform;
import ai.pricefox.mallfox.enums.DataChannelEnum;
import ai.pricefox.mallfox.enums.ProductPlatformEnum;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TripartitePlatformService extends IService<TripartitePlatform> {

    /**
     * 根据平台名称获取平台信息
     *
     * @param platformName    平台名称
     * @param dataChannelEnum 数据来源
     * @return 平台信息
     */
    TripartitePlatform getByPlatformCode(ProductPlatformEnum platformName, DataChannelEnum dataChannelEnum);

    /**
     * 获取所有平台信息
     *
     * @return 所有平台信息
     */
    List<TripartitePlatform> getAll();
}
