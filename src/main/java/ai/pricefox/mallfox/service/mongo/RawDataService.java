package ai.pricefox.mallfox.service.mongo;

import ai.pricefox.mallfox.domain.standard.mongo.RawData;
import ai.pricefox.mallfox.enums.DataChannelEnum;
import ai.pricefox.mallfox.enums.ProductPlatformEnum;
import ai.pricefox.mallfox.repository.mongo.RawDataRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;

// 数据存储服务
@Service
@RequiredArgsConstructor
public class RawDataService {
    private final RawDataRepository rawDataRepository;
    private final ProductIdSequenceService productIdSequenceService;

    /**
     * 保存原始数据
     *
     * @param platform  平台
     * @param channel   渠道
     * @param spu       spu
     * @param sku       sku
     * @param dataJson  原始数据
     * @param productId 自建商品ID
     * @return id
     */
    public String saveRawData(ProductPlatformEnum platform, DataChannelEnum channel, String spu, String sku, String dataJson, String productId) {
        RawData rawData = new RawData();
        rawData.setProductPlatform(platform.name());
        rawData.setDataChannel(channel.name());
        rawData.setSpu(spu);
        rawData.setSku(sku);
        rawData.setDataJson(dataJson);
        rawData.setProductIdentifier(productId);
        rawData.setCreateTime(new Date());
        rawData.setStandardSpu(generateUniqueSpu());
        rawData.setStandardSku(generateUniqueSku());
        return rawDataRepository.save(rawData).getId();
    }

    /**
     * 生成唯一的标准SPU编码
     *
     * @return 唯一的SPU编码
     */
    public String generateUniqueSpu() {
        return productIdSequenceService.generateUniqueSpu();
    }

    /**
     * 生成唯一的标准SKU编码
     *
     * @return 唯一的SKU编码
     */
    public String generateUniqueSku() {
        return productIdSequenceService.generateUniqueSku();
    }
}