package ai.pricefox.mallfox.service.mongo;

import ai.pricefox.mallfox.domain.standard.mongo.RawData;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.concurrent.atomic.AtomicLong;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 商品ID序列号服务
 * 用于生成全局唯一的SPU和SKU编码
 *
 * <AUTHOR>
 * @since 2025/8/2
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductIdSequenceService {

    private final MongoTemplate mongoTemplate;

    /**
     * SPU序列号计数器
     */
    private final AtomicLong spuSequence = new AtomicLong(0);

    /**
     * SKU序列号计数器
     */
    private final AtomicLong skuSequence = new AtomicLong(0);

    /**
     * SPU编码格式的正则表达式
     */
    private static final Pattern SPU_PATTERN = Pattern.compile("^PP(\\d{8})$");

    /**
     * SKU编码格式的正则表达式
     */
    private static final Pattern SKU_PATTERN = Pattern.compile("^PS(\\d{8})$");

    /**
     * 初始化序列号计数器
     * 通过查询MongoDB中现有数据的最大序列号来初始化
     */
    @PostConstruct
    public void initSequences() {
        log.info("开始初始化SPU和SKU序列号计数器");

        // 初始化SPU序列号
        long maxSpuSequence = findMaxSequenceFromMongoDB("standardSpu", SPU_PATTERN);
        spuSequence.set(maxSpuSequence);
        log.info("SPU序列号计数器初始化完成，当前值: {}", spuSequence.get());

        // 初始化SKU序列号
        long maxSkuSequence = findMaxSequenceFromMongoDB("standardSku", SKU_PATTERN);
        skuSequence.set(maxSkuSequence);
        log.info("SKU序列号计数器初始化完成，当前值: {}", skuSequence.get());
    }

    /**
     * 从MongoDB中查找指定字段的最大序列号
     *
     * @param fieldName 字段名 (spu 或 sku)
     * @param pattern   编码格式正则表达式
     * @return 最大序列号
     */
    private long findMaxSequenceFromMongoDB(String fieldName, Pattern pattern) {
        long maxSequence = 0L;

        try {
            // 构建查询，按字段降序排列，只取第一个文档
            Query query = new Query().with(Sort.by(Sort.Direction.DESC, fieldName)).limit(1);
            RawData latestData = mongoTemplate.findOne(query, RawData.class);

            if (latestData != null) {
                String fieldValue = null;
                if ("standardSpu".equals(fieldName)) {
                    fieldValue = latestData.getSpu();
                } else if ("standardSku".equals(fieldName)) {
                    fieldValue = latestData.getSku();
                }

                if (fieldValue != null) {
                    Matcher matcher = pattern.matcher(fieldValue);
                    if (matcher.matches()) {
                        maxSequence = Long.parseLong(matcher.group(1));
                    }
                }
            }

            // 确保序列号至少从1开始
            maxSequence = Math.max(maxSequence, 1L);
        } catch (Exception e) {
            log.error("从MongoDB查询最大序列号时发生错误", e);
            // 发生异常时使用默认值
            maxSequence = System.currentTimeMillis() % 100000000;
        }

        return maxSequence;
    }

    /**
     * 获取下一个SPU序列号
     *
     * @return SPU序列号
     */
    public long getNextSpuSequence() {
        return spuSequence.incrementAndGet();
    }

    /**
     * 获取下一个SKU序列号
     *
     * @return SKU序列号
     */
    public long getNextSkuSequence() {
        return skuSequence.incrementAndGet();
    }

    /**
     * 生成唯一的标准SPU编码
     * 格式：PP + 8位数字（递增）
     *
     * @return 唯一的SPU编码
     */
    public String generateUniqueSpu() {
        long sequence = getNextSpuSequence();
        return String.format("PP%08d", sequence % 100000000);
    }

    /**
     * 生成唯一的标准SKU编码
     * 格式：PS + 8位数字（递增）
     *
     * @return 唯一的SKU编码
     */
    public String generateUniqueSku() {
        long sequence = getNextSkuSequence();
        return String.format("PS%08d", sequence % 100000000);
    }
}