package ai.pricefox.mallfox.service.rules;

import ai.pricefox.mallfox.domain.standard.*;
import ai.pricefox.mallfox.service.mongo.RawDataService;
import ai.pricefox.mallfox.enums.RuleGroup;
import ai.pricefox.mallfox.enums.StandardizationSubStepEnum;
import ai.pricefox.mallfox.enums.ProductPlatformEnum;
import ai.pricefox.mallfox.enums.DataChannelEnum;
import ai.pricefox.mallfox.event.StandardizationStepEvent;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import com.google.gson.Gson;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.kie.api.runtime.KieContainer;
import org.kie.api.runtime.KieSession;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据标准化服务
 *
 * <p>该服务使用Drools规则引擎处理不同平台的商品数据，将其转换为标准格式。</p>
 *
 * <p>主要功能包括：</p>
 * <ul>
 *   <li>执行字段映射，将平台特定字段映射到标准字段</li>
 *   <li>应用Drools规则进行数据标准化处理</li>
 *   <li>整合各类标准化规则和数据（品类映射、归一化规则、标准库等）</li>
 * </ul>
 *
 * <p>工作流程：</p>
 * <ol>
 *   <li>首先进行字段映射，将平台数据转换为初步的标准数据结构</li>
 *   <li>初始化Drools规则引擎会话</li>
 *   <li>将数据和所有相关规则插入到规则引擎中</li>
 *   <li>执行规则引擎进行数据标准化处理</li>
 *   <li>返回标准化后的数据</li>
 * </ol>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@AllArgsConstructor
public class DataStandardizationService {

    private final KieContainer kieContainer;
    private final StandardDataCacheService standardDataCacheService;
    private final ApplicationEventPublisher eventPublisher;
    private final RawDataService rawDataService;

    /**
     * 启动分步标准化流程
     *
     * @param productDataMap 平台商品数据
     */
    public void startStepByStepStandardizationWithJson(Map<String, Object> productDataMap) {
        if (productDataMap == null || !productDataMap.containsKey("sourcePlatform")) {
            log.error("[分步标准化] 产品数据或平台信息为空");
            throw new IllegalArgumentException("产品数据和平台信息不能为空");
        }

        log.info("[分步标准化] 开始启动分步标准化流程: sourcePlatform={}", productDataMap.get("sourcePlatform"));

        try {
            // 查询平台信息
            ProductPlatformEnum sourcePlatform = ProductPlatformEnum.valueOf(productDataMap.get("sourcePlatform").toString());
            DataChannelEnum dataChannel = DataChannelEnum.valueOf(productDataMap.get("dataChannel").toString());
            List<TripartitePlatform> tripartitePlatform = standardDataCacheService.getTripartitePlatform();
            TripartitePlatform platform = tripartitePlatform.stream().filter(t -> t.getEnable() && t.getPlatformName().equals(sourcePlatform)).findFirst().orElse(null);
            if (platform == null) {
                log.error("[分步标准化] 未找到平台信息: sourcePlatform={}, dataChannel={}", sourcePlatform, dataChannel);
                throw new IllegalArgumentException("未找到平台信息");
            }
            productDataMap.put("platformCode", platform.getPlatformCode());
            log.debug("[分步标准化] 平台信息查询完成: platformCode={},platformName={},type={}", platform.getPlatformCode(), platform.getPlatformName(), platform.getEnable());

            // 发布字段映射完成事件，触发下一步处理
            log.info("[分步标准化] 发布数据合并事件");
            DynamicStandardProduct dynamicStandardProduct = new DynamicStandardProduct();
            dynamicStandardProduct.setSourceType(dataChannel);
            dynamicStandardProduct.setPlatformCode(platform.getPlatformCode());
            dynamicStandardProduct.setPlatformName(platform.getPlatformName());
            dynamicStandardProduct.setOriginData(productDataMap);

            // 1.查询源数据中的分类信息，默认字段名为category去模糊匹配，后面应该在管理端去维护
            String searchKey = "category";
            Map<String, Object> resultMap = productDataMap.entrySet().stream().filter(entry -> entry.getKey().toLowerCase().contains(searchKey)).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

            // 2.查询平台映射分类
            List<StandardCategoryMapping> standardCategoryMappingCacheList = standardDataCacheService.getPlatformCategoryMapping();
            if (standardCategoryMappingCacheList == null || standardCategoryMappingCacheList.isEmpty()) {
                log.error("[分步标准化] 未查询到平台分类映射: sourcePlatform={}, dataChannel={}", sourcePlatform, dataChannel);
                return;
            }
            List<StandardCategoryMapping> standardCategoryMappingList = standardCategoryMappingCacheList.stream().filter(s -> s.getPlatformCode().equals(platform.getPlatformCode()) && !s.getDeleted()).toList();
            if (standardCategoryMappingList.isEmpty()) {
                log.error("[分步标准化] 未找到[{}]映射分类 ", sourcePlatform);
                return;
            }
            // 3.匹配出最优的分类
            StandardCategoryMapping maxPlatformLevel = findMaxPlatformLevel(standardCategoryMappingList, resultMap);
            if (maxPlatformLevel == null) {
                log.error("[分步标准化] 未找到[{}]映射分类 ", sourcePlatform);
                return;
            }
            // 4.查询标准分类
            List<StandardCategory> categoryList = standardDataCacheService.getPlatformCategory();
            StandardCategory standardCategory = categoryList.stream().filter(s -> s.getCategoryCode().equals(maxPlatformLevel.getStandardCategoryCode())).findFirst().orElse(null);
            if (standardCategory == null) {
                log.error("[分步标准化] 未找到标准分类: categoryMap={}", resultMap);
                return;
            }

            // 5.查询标准属性映射
            List<StandardAttribute> attributeList = standardDataCacheService.getStandardAttribute();
            List<StandardAttribute> standardAttributeList = attributeList.stream().filter(s -> s.getStandardCategoryCode().equals(standardCategory.getCategoryCode())).toList();
            // 6.获取spu 和sku 对应的属性编码
            StandardAttribute spuAttribute = standardAttributeList.stream().filter(s -> s.getAttributeNameEn().equalsIgnoreCase("SPU ID")).findFirst().orElse(null);
            StandardAttribute skuAttribute = standardAttributeList.stream().filter(s -> s.getAttributeNameEn().equals("SKU ID")).findFirst().orElse(null);
            if (spuAttribute == null || skuAttribute == null) {
                log.error("[分步标准化] 未找到spu和sku属性: standardAttributeList={}", standardAttributeList);
                return;
            }
            // 7.获取属性名映射
            List<StandardAttributeMapping> attributeMappingList = standardDataCacheService.getAttributeMapping();
            List<StandardAttributeMapping> spuAttributeMappingList = attributeMappingList.stream().filter(s -> s.getAttributeCode().equals(spuAttribute.getAttributeCode())).toList();
            List<StandardAttributeMapping> skuAttributeMappingList = attributeMappingList.stream().filter(s -> s.getAttributeCode().equals(skuAttribute.getAttributeCode())).toList();
            if (spuAttributeMappingList.isEmpty() && skuAttributeMappingList.isEmpty()) {
                log.error("[分步标准化] 未找到属性值映射");
                return;
            }

            // 生成产品标识并保存原始数据
            String spu = findMatchingValue(productDataMap, spuAttributeMappingList);
            String sku = findMatchingValue(productDataMap, skuAttributeMappingList);

            String productIdentifier = generateProductIdentifierFromMap(spu, sku, dynamicStandardProduct.getSourceType(), dynamicStandardProduct.getPlatformName());
            Gson gson = new Gson();
            String dataId = rawDataService.saveRawData(sourcePlatform, dataChannel, spu, sku, gson.toJson(productDataMap), productIdentifier);
            dynamicStandardProduct.setDataId(dataId);
            // 发布数据合并事件
            eventPublisher.publishEvent(new StandardizationStepEvent(this, platform.getPlatformCode(), null, StandardizationSubStepEnum.MARGE_DATA, dynamicStandardProduct));
            log.info("[分步标准化] 数据合并事件发布完成");
        } catch (Exception e) {
            log.error("[分步标准化] 平台信息类型错误: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 执行单个标准化步骤
     *
     * @param step                   步骤类型
     * @param dynamicStandardProduct 过程数据
     * @return 处理后的数据
     */
    public DynamicStandardProduct executeStandardizationStepWithJson(StandardizationSubStepEnum step, DynamicStandardProduct dynamicStandardProduct) {

        final String LOG_PREFIX = "[标准化步骤] ";

        if (log.isInfoEnabled()) {
            log.info(LOG_PREFIX + "开始执行步骤: {} ({})", step.getDescription(), step.getCode());
        }

        KieSession kieSession = null;

        try {
            kieSession = kieContainer.newKieSession("ksession-rules");
            if (kieSession == null) {
                if (log.isWarnEnabled()) {
                    log.warn(LOG_PREFIX + "无法创建KieSession，跳过规则处理");
                }
                return dynamicStandardProduct;
            }
//            kieSession.getAgenda().getAgendaGroup("debug").setFocus();
            // 插入数据到规则引擎
            if (log.isDebugEnabled()) {
                log.debug(LOG_PREFIX + "插入原始数据到规则引擎");
            }
            kieSession.insert(dynamicStandardProduct.getOriginData());

            if (log.isDebugEnabled()) {
                log.debug(LOG_PREFIX + "插入当前数据到规则引擎");
            }

            if (log.isDebugEnabled()) {
                log.debug(LOG_PREFIX + "插入所有规则和数据到规则引擎");
            }
            insertAllRulesAndData(kieSession);

            // 执行对应规则组
            if (log.isInfoEnabled()) {
                log.info(LOG_PREFIX + "开始执行 【{}】 规则", step.getDescription());
            }

            int firedRules = executeRulesByStep(kieSession, step);

            if (log.isInfoEnabled()) {
                log.info(LOG_PREFIX + "步骤 {} 完成，执行 {} 条规则", step.getDescription(), firedRules);
            }

        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error(LOG_PREFIX + "规则执行错误", e);
            }
        } finally {
            if (kieSession != null) {
                if (log.isDebugEnabled()) {
                    log.debug(LOG_PREFIX + "释放KieSession资源");
                }
                kieSession.dispose();
            }
        }

        return dynamicStandardProduct;
    }

    /**
     * 根据步骤类型执行对应的规则
     *
     * @param kieSession Kie会话
     * @param step       步骤类型
     * @return 执行的规则数量
     */
    private int executeRulesByStep(KieSession kieSession, StandardizationSubStepEnum step) {
        int firedRules = 0;

        switch (step) {
            case MARGE_DATA, FINAL_STANDARDIZATION:
                break;
            case CATEGORY_STANDARDIZATION:
                // 执行品类标准化规则
                log.info("[标准化步骤] 执行品类标准化规则");
                firedRules += kieSession.fireAllRules(createRuleFilter(RuleGroup.CATEGORY_MAPPING));
                break;

            case BRAND_STANDARDIZATION:
                // 执行品牌标准化规则
                log.info("[标准化步骤] 执行品牌标准化规则");
                firedRules += kieSession.fireAllRules(createRuleFilter(RuleGroup.BRAND_MAPPING));
                break;

            case MODEL_STANDARDIZATION:
                // 执行型号标准化规则
                log.info("[标准化步骤] 执行型号标准化规则");
                firedRules += kieSession.fireAllRules(createRuleFilter(RuleGroup.MODEL_MAPPING));
                break;

            case OTHER_STANDARDIZATION:
                // 执行颜色标准化规则
                log.info("[标准化步骤] 执行颜色标准化规则");
                firedRules += kieSession.fireAllRules(createRuleFilter(RuleGroup.ATTRIBUTE_MAPPING));
                break;
            default:
                // 默认执行所有规则
                log.info("[标准化步骤] 执行所有规则");
                firedRules += kieSession.fireAllRules();
                break;
        }

        return firedRules;
    }

    /**
     * 创建规则过滤器
     *
     * @param ruleGroup 规则组
     * @return 规则过滤器
     */
    private org.kie.api.runtime.rule.AgendaFilter createRuleFilter(RuleGroup ruleGroup) {
        return new org.kie.api.runtime.rule.AgendaFilter() {
            @Override
            public boolean accept(org.kie.api.runtime.rule.Match match) {
                String ruleName = match.getRule().getName();

                // 检查规则名称是否完全匹配任何指定的规则名称
                for (String targetRuleName : ruleGroup.getRuleNames()) {
                    if (ruleName.equals(targetRuleName)) {
                        return true;
                    }
                }

                return false;
            }
        };
    }

    /**
     * 生成产品标识符
     *
     * <p>产品标识符由平台代码和SKU ID组成，用于唯一标识一个产品。</p>
     *
     * @param spu                 spu
     * @param sku                 产品sku Id
     * @param dataChannelEnum     渠道
     * @param productPlatformEnum 平台
     * @return 产品标识符，格式为"平台代码_渠道_SKU ID"
     */
    public String generateProductIdentifierFromMap(String spu, String sku, DataChannelEnum dataChannelEnum, ProductPlatformEnum productPlatformEnum) {
        if (productPlatformEnum == null || dataChannelEnum == null) {
            return "unknown_unknown_unknown";
        }

        return productPlatformEnum + "_" + dataChannelEnum + "_" + spu + "_" + sku;
    }

    /**
     * 在已查询的数据列表中匹配品类路径，返回platform_level最大的StandardCategoryMapping对象
     *
     * @param standardCategoryMappingList 已查询的平台分类数据列表
     * @param resultMap                   包含品类信息的Map
     * @return 匹配的StandardCategoryMapping对象，若无匹配返回null
     */
    public StandardCategoryMapping findMaxPlatformLevel(List<StandardCategoryMapping> standardCategoryMappingList, Map<String, Object> resultMap) {

        // 1. 提取所有可能的品类名称（包括categoryPath中的name）
        Set<String> candidateNames = extractCategoryNames(resultMap);
        log.debug("[分类匹配] 提取到的候选分类名称: {}", candidateNames);

        if (candidateNames.isEmpty()) {
            // 尝试直接从resultMap中获取category和categoryPath字段
            extractDirectCategoryFields(resultMap, candidateNames);
            log.debug("[分类匹配] 直接从字段中提取的分类名称: {}", candidateNames);
        }

        if (candidateNames.isEmpty()) {
            log.warn("[分类匹配] 未找到任何分类信息");
            return null;
        }

        // 2. 将候选名称转为小写（用于忽略大小写匹配）
        Set<String> lowerCaseCandidates = candidateNames.stream().map(String::toLowerCase).collect(Collectors.toSet());

        // 3. 创建查找表：小写名称 -> 最佳匹配对象
        Map<String, StandardCategoryMapping> bestMatchMap = new HashMap<>();

        // 4. 遍历所有分类映射，构建最佳匹配表
        for (StandardCategoryMapping mapping : standardCategoryMappingList) {
            String lowerName = mapping.getPlatformCategoryName().toLowerCase();

            // 只处理候选名称列表中的值
            if (lowerCaseCandidates.contains(lowerName)) {
                // 检查是否已有该名称的映射
                StandardCategoryMapping existing = bestMatchMap.get(lowerName);

                // 更新最佳匹配：优先platform_level，其次id
                if (existing == null || isBetterMatch(mapping, existing)) {
                    bestMatchMap.put(lowerName, mapping);
                }
            }
        }

        // 5. 如果没有任何匹配，尝试模糊匹配
        if (bestMatchMap.isEmpty()) {
            log.debug("[分类匹配] 精确匹配失败，尝试模糊匹配");
            for (StandardCategoryMapping mapping : standardCategoryMappingList) {
                String platformCategoryName = mapping.getPlatformCategoryName().toLowerCase();
                for (String candidate : lowerCaseCandidates) {
                    // 检查是否包含关系
                    if (platformCategoryName.contains(candidate) || candidate.contains(platformCategoryName)) {
                        bestMatchMap.put(candidate, mapping);
                        break;
                    }
                }
            }
        }

        // 6. 如果仍然没有任何匹配，返回null
        if (bestMatchMap.isEmpty()) {
            log.warn("[分类匹配] 未找到匹配的分类映射，候选名称: {}", candidateNames);
            return null;
        }

        log.debug("[分类匹配] 找到 {} 个匹配项", bestMatchMap.size());

        // 7. 在所有匹配中找出platform_level最大的对象
        StandardCategoryMapping result = bestMatchMap.values().stream().max(Comparator.comparingInt(StandardCategoryMapping::getPlatformLevel).thenComparingLong(StandardCategoryMapping::getId)).orElse(null);

        log.debug("[分类匹配] 最佳匹配结果: {}", result);
        return result;
    }

    /**
     * 直接从resultMap中提取category和categoryPath字段
     */
    private void extractDirectCategoryFields(Map<String, Object> resultMap, Set<String> candidateNames) {
        // 处理category字段
        Object categoryObj = resultMap.get("category");
        if (categoryObj instanceof List) {
            List<?> categoryList = (List<?>) categoryObj;
            for (Object item : categoryList) {
                if (item instanceof String) {
                    addStringValue((String) item, candidateNames);
                }
            }
        }

        // 处理categoryPath字段
        Object categoryPathObj = resultMap.get("categoryPath");
        if (categoryPathObj instanceof List) {
            extractNamesFromCategoryPath((List<?>) categoryPathObj, candidateNames);
        }
    }

    /**
     * 判断新映射是否比现有映射更好
     */
    private boolean isBetterMatch(StandardCategoryMapping newMapping, StandardCategoryMapping existingMapping) {
        int levelCompare = Integer.compare(newMapping.getPlatformLevel(), existingMapping.getPlatformLevel());

        if (levelCompare > 0) {
            return true;
        } else if (levelCompare == 0) {
            return newMapping.getId() > existingMapping.getId();
        }
        return false;
    }

    /**
     * 从resultMap中提取所有品类名称
     * 支持多种格式：
     * 1. 普通字符串值
     * 2. categoryPath数组中的name字段
     * 3. category数组中的字符串值
     * 4. 嵌套结构中的字符串值
     */
    private Set<String> extractCategoryNames(Map<String, Object> resultMap) {
        Set<String> names = new HashSet<>();

        for (Map.Entry<String, Object> entry : resultMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            // 处理categoryPath数组
            if ("categoryPath".equalsIgnoreCase(key) && value instanceof List) {
                extractNamesFromCategoryPath((List<?>) value, names);
            }
            // 处理category数组
            else if ("category".equalsIgnoreCase(key) && value instanceof List) {
                extractNamesFromList((List<?>) value, names);
            }
            // 处理普通字符串值
            else if (value instanceof String) {
                addStringValue((String) value, names);
            }
            // 处理嵌套Map
            else if (value instanceof Map) {
                extractNamesFromMap((Map<?, ?>) value, names);
            }
            // 处理其他类型的List
            else if (value instanceof List) {
                extractNamesFromList((List<?>) value, names);
            }
        }

        return names;
    }

    /**
     * 从categoryPath数组中提取name值
     */
    private void extractNamesFromCategoryPath(List<?> categoryPath, Set<String> names) {
        if (categoryPath == null) return;

        for (Object item : categoryPath) {
            if (item instanceof Map) {
                Map<?, ?> itemMap = (Map<?, ?>) item;
                Object nameObj = itemMap.get("name");
                if (nameObj instanceof String) {
                    addStringValue((String) nameObj, names);
                }
            }
            // 支持简单字符串形式的路径节点
            else if (item instanceof String) {
                addStringValue((String) item, names);
            }
        }
    }

    /**
     * 从嵌套Map中提取字符串值
     */
    private void extractNamesFromMap(Map<?, ?> map, Set<String> names) {
        for (Object value : map.values()) {
            if (value instanceof String) {
                addStringValue((String) value, names);
            } else if (value instanceof Map) {
                extractNamesFromMap((Map<?, ?>) value, names);
            } else if (value instanceof List) {
                extractNamesFromList((List<?>) value, names);
            }
        }
    }

    /**
     * 从列表中提取字符串值
     * 支持两种格式：
     * 1. 直接字符串数组：["Cell Phones", "Accessories"]
     * 2. 包含Map的数组：[{"name": "Cell Phones"}, {"name": "Accessories"}]
     */
    private void extractNamesFromList(List<?> list, Set<String> names) {
        if (list == null) return;

        for (Object item : list) {
            if (item instanceof String) {
                addStringValue((String) item, names);
            } else if (item instanceof Map) {
                // 尝试从Map中提取name字段
                Map<?, ?> itemMap = (Map<?, ?>) item;
                Object nameObj = itemMap.get("name");
                if (nameObj instanceof String) {
                    addStringValue((String) nameObj, names);
                } else {
                    // 递归处理嵌套结构
                    extractNamesFromMap(itemMap, names);
                }
            } else if (item instanceof List) {
                extractNamesFromList((List<?>) item, names);
            }
        }
    }

    /**
     * 添加有效的字符串值到名称集合
     */
    private void addStringValue(String value, Set<String> names) {
        String trimmed = value.trim();
        if (!trimmed.isEmpty()) {
            names.add(trimmed);
        }
    }

    /**
     * 插入所有规则和数据到规则引擎
     *
     * <p>将所有需要的规则和数据一次性插入到规则引擎中，包括：</p>s
     * <ul>
     *   <li>平台品类映射规则</li>
     *   <li>归一化规则</li>
     *   <li>无效值模式</li>
     *   <li>各类标准库数据</li>
     * </ul>
     *
     * @param kieSession 规则引擎会话
     */
    private void insertAllRulesAndData(KieSession kieSession) {
        log.debug("[规则引擎] 开始插入规则和数据到规则引擎");

        // 插入平台字段映射规则
        try {
            log.debug("[规则引擎] 开始插入平台字段映射规则");
            List<?> standardAttributeList = standardDataCacheService.getStandardAttribute();
            standardAttributeList.forEach(kieSession::insert);
            log.trace("[规则引擎] 插入 {} 条平台字段映射规则", standardAttributeList.size());
        } catch (Exception e) {
            log.error("[规则引擎] 开始插入平台字段映射规则时出错", e);
        }

        // 插入平台品类映射规则
        log.debug("[规则引擎] 开始插入平台品类映射规则");
        insertCategoryMappingRules(kieSession);

        // 插入标准库数据
        log.debug("[规则引擎] 开始插入标准库数据");
        insertStandardLibraries(kieSession);

        log.debug("[规则引擎] 规则和数据插入完成");
    }

    /**
     * 插入平台品类映射规则到规则引擎
     *
     * <p>平台品类映射规则用于将不同平台的品类映射到统一的标准品类体系。</p>
     *
     * @param kieSession 规则引擎会话
     */
    private void insertCategoryMappingRules(KieSession kieSession) {
        // 获取所有平台品类映射规则
        try {
            log.debug("[规则引擎] 获取平台品类映射规则");
            List<?> categoryMappings = standardDataCacheService.getPlatformCategoryMapping();
            categoryMappings.forEach(kieSession::insert);
            log.trace("[规则引擎] 插入 {} 条平台品类映射规则", categoryMappings.size());
        } catch (Exception e) {
            log.error("[规则引擎] 插入平台品类映射规则时出错", e);
        }
    }

    /**
     * 插入标准库数据到规则引擎
     *
     * <p>标准库数据包括标准品牌、标准品类、标准颜色等，用于数据标准化过程中的参考。</p>
     *
     * @param kieSession 规则引擎会话
     */
    private void insertStandardLibraries(KieSession kieSession) {
        // 插入所有标准库数据
        try {
            log.debug("[规则引擎] 获取标准库数据");
            // 定义标准库服务和描述信息
            List<StandardLibraryInfo> standardLibraries = Arrays.asList(
                    // 插入属性
            );

            // 插入所有标准库数据
            standardLibraries.forEach(libInfo -> {
                libInfo.getData().forEach(kieSession::insert);
                log.trace("[规则引擎] 插入 {} 条标准{}库数据", libInfo.getData().size(), libInfo.getDescription());
            });

        } catch (Exception e) {
            log.error("[规则引擎] 插入标准库数据时出错", e);
        }
    }

    /**
     * 标准库信息内部类
     * 用于封装标准库数据和服务描述
     */
    @Getter
    private static class StandardLibraryInfo {
        private final List<?> data;
        private final String description;

        public StandardLibraryInfo(List<?> data, String description) {
            this.data = data;
            this.description = description;
        }

    }

    /**
     * 保存处理数据结果
     *
     * @param skuId skuId
     * @param data  处理数据
     * @param step  处理步骤
     */
    public void saveProcessedData(String skuId, DynamicStandardProduct data, StandardizationSubStepEnum step) {
    }

    /**
     * 记录步骤完成
     *
     * @param productIdentifier 产品标识符
     * @param step              处理步骤
     * @param result            处理结果
     * @param success           是否成功
     */
    public void recordStepCompletion(String productIdentifier, StandardizationSubStepEnum step, DynamicStandardProduct result, boolean success) {
        // 实现状态记录逻辑
        if (success) {
            log.info("[状态记录] 步骤{}处理成功: 产品标识={}", step.name(), productIdentifier);

            // 示例：更新数据库状态
        } else {
            log.warn("[状态记录] 步骤{}处理失败: 产品标识={}", step.name(), productIdentifier);

            // 示例：记录失败信息

            // 可选：抛出异常或触发告警
        }
    }

    /**
     * 从产品数据映射中查找与属性映射列表中mappingName匹配的值
     *
     * @param productDataMap       产品数据映射
     * @param attributeMappingList 属性映射列表
     * @return 匹配的值，如果未找到则返回null
     */
    private String findMatchingValue(Map<String, Object> productDataMap, List<StandardAttributeMapping> attributeMappingList) {
        if (productDataMap == null || attributeMappingList == null) {
            return null;
        }

        // 遍历属性映射列表，查找与productDataMap中的键匹配的mappingName
        for (StandardAttributeMapping mapping : attributeMappingList) {
            String mappingName = mapping.getMappingName();
            if (mappingName != null && productDataMap.containsKey(mappingName)) {
                return productDataMap.get(mappingName).toString();
            }
        }

        // 如果在精确匹配中未找到，则尝试忽略大小写的匹配
        for (StandardAttributeMapping mapping : attributeMappingList) {
            String mappingName = mapping.getMappingName();
            if (mappingName != null) {
                for (Map.Entry<String, Object> entry : productDataMap.entrySet()) {
                    if (mappingName.equalsIgnoreCase(entry.getKey())) {
                        return entry.getValue().toString();
                    }
                }
            }
        }

        return null;
    }

}
