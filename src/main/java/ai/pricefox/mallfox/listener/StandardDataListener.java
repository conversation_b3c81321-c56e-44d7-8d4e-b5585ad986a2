package ai.pricefox.mallfox.listener;

import ai.pricefox.mallfox.event.StandardDataEvent;
import ai.pricefox.mallfox.service.rules.DataStandardizationService;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.util.Map;

/**
 * 标准化数据监听器
 * 通过统一事件处理机制优化代码重复性，支持异常处理、日志增强等优化特性
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StandardDataListener {
    private final DataStandardizationService dataStandardizationService;

    /**
     * 标准化数据监听器
     */
    @EventListener
    public void handleDataListener(StandardDataEvent event) {
        log.info("[控制器] 启动分步标准化处理");
        // 启动分步标准化处理
        Gson gson = new Gson();
        Type stringObjectMap = new TypeToken<Map<String, Object>>(){}.getType();
        Map<String, Object> originalDataMap = gson.fromJson(event.getOriginalData(), stringObjectMap);
        dataStandardizationService.startStepByStepStandardizationWithJson(originalDataMap);
        log.info("[控制器] 分步标准化已启动");
    }

}
