package ai.pricefox.mallfox.listener;

import ai.pricefox.mallfox.service.mongo.RawDataService;
import ai.pricefox.mallfox.enums.StandardizationSubStepEnum;
import ai.pricefox.mallfox.event.StandardizationStepEvent;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.service.rules.DataStandardizationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 标准化步骤监听器
 * 通过统一事件处理机制优化代码重复性，支持异常处理、日志增强等优化特性
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StandardizationStepListener {

    private final DataStandardizationService dataStandardizationService;
    private final ApplicationEventPublisher eventPublisher;
    private final RawDataService rawDataService;

    /**
     * 处理数据合并步骤事件
     * 包含特殊处理逻辑：字段配置查找和源数据保存
     *
     * @param event 标准化步骤事件
     */
    @EventListener
    public void handleMargeDataStep(StandardizationStepEvent event) {
        try {
            if (event.getCurrentStep() != StandardizationSubStepEnum.MARGE_DATA) {
                return;
            }

            DynamicStandardProduct dynamicStandardProduct = event.getDynamicStandardProduct();
            log.debug("[标准化步骤] 数据合并步骤前的数据: {}", dynamicStandardProduct.getOriginData());

            // 保存处理结果并记录状态
            dataStandardizationService.saveProcessedData(event.getSkuId(), dynamicStandardProduct, StandardizationSubStepEnum.MARGE_DATA);
            dataStandardizationService.recordStepCompletion(event.getSkuId(), StandardizationSubStepEnum.MARGE_DATA, dynamicStandardProduct, true);

        } catch (Exception e) {
            log.error("[标准化步骤] 数据合并步骤发生异常，skuId={}, platformCode={}", event.getSkuId(), event.getPlatformCode(), e);
            dataStandardizationService.recordStepCompletion(event.getSkuId(), StandardizationSubStepEnum.MARGE_DATA, null, false);
        }
    }

    /**
     * 统一处理标准化步骤事件
     * 通过步骤枚举与步骤名称的映射关系处理重复性代码
     *
     * @param event 标准化步骤事件
     */
    @EventListener
    public void handleStandardizationStep(StandardizationStepEvent event) {
        DynamicStandardProduct processedData = new DynamicStandardProduct();
        try {
            // 检查是否为通用处理步骤
            if (!event.getCurrentStep().equals(StandardizationSubStepEnum.OTHER_STANDARDIZATION)) {
                return;
            }

            String stepName = event.getCurrentStep().getDescription();
            log.info("[标准化步骤] 开始处理{}步骤: skuId={}, platformCode={}", stepName, event.getSkuId(), event.getPlatformCode());
            log.debug("[标准化步骤] {}步骤前的数据: {}", stepName, event.getDynamicStandardProduct().getCurrentData());

            // 执行标准化规则
            processedData = dataStandardizationService.executeStandardizationStepWithJson(event.getCurrentStep(), event.getDynamicStandardProduct());
            log.debug("[标准化步骤] {}步骤后的数据: {}", stepName, processedData.getCurrentData());
            log.info("[标准化步骤] {}步骤处理完成: skuId={}, platformCode={}", stepName,processedData.getProductIdentifier(), event.getPlatformCode());

            // 保存处理结果
            dataStandardizationService.saveProcessedData(processedData.getProductIdentifier(), processedData, event.getCurrentStep());

            // 记录处理状态
            dataStandardizationService.recordStepCompletion(processedData.getProductIdentifier(), event.getCurrentStep(), processedData, true);

        } catch (Exception e) {
            log.error("[标准化步骤] 处理标准化步骤发生异常，step={}, skuId={}, platformCode={}", event.getCurrentStep(), processedData.getProductIdentifier(), event.getPlatformCode(), e);
            dataStandardizationService.recordStepCompletion(processedData.getProductIdentifier(), event.getCurrentStep(), null, false);
        }
    }

    /**
     * 处理最终标准化步骤事件
     *
     * @param event 标准化步骤事件
     */
    @EventListener
    public void handleFinalStandardizationStep(StandardizationStepEvent event) {
        try {
            if (event.getCurrentStep() != StandardizationSubStepEnum.FINAL_STANDARDIZATION) {
                return;
            }

            log.info("[标准化步骤] 开始处理最终标准化步骤: skuId={}, platformCode={}", event.getSkuId(), event.getPlatformCode());
            log.debug("[标准化步骤] 最终标准化步骤前的数据: {}", event.getDynamicStandardProduct().getCurrentData());

            // 执行最终标准化规则组
            DynamicStandardProduct processedData = dataStandardizationService.executeStandardizationStepWithJson(StandardizationSubStepEnum.FINAL_STANDARDIZATION, event.getDynamicStandardProduct());

            log.debug("[标准化步骤] 最终标准化步骤后的数据: {}", processedData.getCurrentData());
            log.info("[标准化步骤] 最终标准化步骤处理完成: skuId={}, platformCode={}", event.getSkuId(), event.getPlatformCode());

            // 保存处理结果
            dataStandardizationService.saveProcessedData(event.getSkuId(), processedData, StandardizationSubStepEnum.FINAL_STANDARDIZATION);

            // 发布标准化完成事件
            eventPublisher.publishEvent(new StandardizationStepEvent(this, event.getPlatformCode(),event.getSkuId(),  StandardizationSubStepEnum.FINAL_STANDARDIZATION, processedData));
            // 记录处理状态
            dataStandardizationService.recordStepCompletion(event.getSkuId(), StandardizationSubStepEnum.FINAL_STANDARDIZATION, processedData, true);

        } catch (Exception e) {
            log.error("[标准化步骤] 最终标准化步骤发生异常，skuId={}, platformCode={}", event.getSkuId(), event.getPlatformCode(), e);
            dataStandardizationService.recordStepCompletion(event.getSkuId(), StandardizationSubStepEnum.FINAL_STANDARDIZATION, null, false);
        }
    }
}
