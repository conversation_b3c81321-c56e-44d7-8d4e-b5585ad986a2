package ai.pricefox.mallfox.listener;

import ai.pricefox.mallfox.domain.standard.PlatformFieldMapping;
import ai.pricefox.mallfox.domain.standard.mongo.RawDataService;
import ai.pricefox.mallfox.enums.DataChannelEnum;
import ai.pricefox.mallfox.enums.ProductPlatformEnum;
import ai.pricefox.mallfox.enums.StandardizationSubStepEnum;
import ai.pricefox.mallfox.event.StandardizationCompletedEvent;
import ai.pricefox.mallfox.event.StandardizationStepEvent;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.service.rules.DataStandardizationService;
import com.google.gson.Gson;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 标准化步骤监听器
 * 通过统一事件处理机制优化代码重复性，支持异常处理、日志增强等优化特性
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StandardizationStepListener {

    private final DataStandardizationService dataStandardizationService;
    private final ApplicationEventPublisher eventPublisher;
    private final RawDataService rawDataService;

    /**
     * 处理数据合并步骤事件
     * 包含特殊处理逻辑：字段配置查找和源数据保存
     *
     * @param event 标准化步骤事件
     */
    @EventListener
    public void handleMargeDataStep(StandardizationStepEvent event) {
        try {
            if (event.getCurrentStep() != StandardizationSubStepEnum.MARGE_DATA) {
                return;
            }

            // 获取并验证字段映射配置
            PlatformFieldMapping platformFieldMapping = event.getDynamicStandardProduct().getFieldMappingList().stream().filter(Objects::nonNull).filter(s -> s.getPlatformCode() != null && s.getStandardFieldCode() != null).filter(s -> s.getPlatformCode().equals(event.getPlatformCode()) && s.getStandardFieldCode().equals("Raw0000003")).findFirst().orElse(null);

            if (platformFieldMapping == null) {
                log.error("[标准化步骤] 未找到原始SKU字段配置，无法生成产品标识，platformCode={}", event.getPlatformCode());
                return;
            }

            String sourceFieldName = platformFieldMapping.getSourceFieldName();
            if (sourceFieldName == null || sourceFieldName.isEmpty()) {
                log.error("[标准化步骤] 字段映射配置的sourceFieldName为空，platformCode={}", event.getPlatformCode());
                dataStandardizationService.recordStepCompletion(event.getSkuId(), StandardizationSubStepEnum.MARGE_DATA, null, false);
                return;
            }

            // 执行字段映射规则组
            DynamicStandardProduct dynamicStandardProduct = dataStandardizationService.executeStandardizationStepWithJson(StandardizationSubStepEnum.FIELD_MAPPING, event.getOriginalDataMap(), event.getDynamicStandardProduct());

            // 设置平台和渠道信息
            ProductPlatformEnum productPlatformEnum = ProductPlatformEnum.valueOf(event.getOriginalDataMap().get("sourcePlatform").toString());
            DataChannelEnum dataChannelEnum = DataChannelEnum.valueOf(event.getOriginalDataMap().get("dataChannel").toString());
            dynamicStandardProduct.setPlatformName(productPlatformEnum);
            dynamicStandardProduct.setSourceType(dataChannelEnum);

            // 生成产品标识并保存原始数据
            String productIdentifier = dataStandardizationService.generateProductIdentifierFromMap(event.getOriginalDataMap().get(sourceFieldName).toString(), dynamicStandardProduct.getSourceType(), dynamicStandardProduct.getPlatformName());

            log.info("[标准化步骤] 开始处理数据合并步骤: 产品标识={}, platformCode={}", productIdentifier, event.getPlatformCode());
            log.debug("[标准化步骤] 数据合并步骤前的数据: {}", event.getOriginalDataMap());

            Gson gson = new Gson();
            String dataId = rawDataService.saveRawData(event.getOriginalDataMap().get("platformCode").toString(), productIdentifier, gson.toJson(event.getOriginalDataMap()));
            dynamicStandardProduct.setDataId(dataId);
            dynamicStandardProduct.setProductIdentifier(productIdentifier);
            // 保存处理结果并记录状态
            dataStandardizationService.saveProcessedData(sourceFieldName, dynamicStandardProduct, StandardizationSubStepEnum.MARGE_DATA);

            dataStandardizationService.recordStepCompletion(sourceFieldName, StandardizationSubStepEnum.MARGE_DATA, dynamicStandardProduct, true);

        } catch (Exception e) {
            log.error("[标准化步骤] 数据合并步骤发生异常，skuId={}, platformCode={}", event.getDynamicStandardProduct().getStringField("skuId"), event.getPlatformCode(), e);
            dataStandardizationService.recordStepCompletion(event.getDynamicStandardProduct().getStringField("skuId"), StandardizationSubStepEnum.MARGE_DATA, null, false);
        }
    }

    /**
     * 统一处理标准化步骤事件
     * 通过步骤枚举与步骤名称的映射关系处理重复性代码
     *
     * @param event 标准化步骤事件
     */
    @EventListener
    public void handleStandardizationStep(StandardizationStepEvent event) {
        try {
            // 检查是否为通用处理步骤
            if (!event.getCurrentStep().equals(StandardizationSubStepEnum.OTHER_STANDARDIZATION)) {
                return;
            }

            String stepName = event.getCurrentStep().getDescription();
            log.info("[标准化步骤] 开始处理{}步骤: skuId={}, platformCode={}", stepName, event.getDynamicStandardProduct().getStringField("skuId"), event.getPlatformCode());
            log.debug("[标准化步骤] {}步骤前的数据: {}", stepName, event.getDynamicStandardProduct().getFields());

            // 执行标准化规则
            DynamicStandardProduct processedData = dataStandardizationService.executeStandardizationStepWithJson(event.getCurrentStep(), event.getOriginalDataMap(), event.getDynamicStandardProduct());
            log.debug("[标准化步骤] {}步骤后的数据: {}", stepName, processedData.getFields());
            log.info("[标准化步骤] {}步骤处理完成: skuId={}, platformCode={}", stepName, event.getDataId(), event.getPlatformCode());

            // 保存处理结果
            dataStandardizationService.saveProcessedData(event.getDataId(), processedData, event.getCurrentStep());

            // 记录处理状态
            dataStandardizationService.recordStepCompletion(event.getDataId(), event.getCurrentStep(), processedData, true);

        } catch (Exception e) {
            log.error("[标准化步骤] 处理标准化步骤发生异常，step={}, skuId={}, platformCode={}", event.getCurrentStep(), event.getDataId(), event.getPlatformCode(), e);
            dataStandardizationService.recordStepCompletion(event.getDataId(), event.getCurrentStep(), null, false);
        }
    }

    /**
     * 处理最终标准化步骤事件
     *
     * @param event 标准化步骤事件
     */
    @EventListener
    public void handleFinalStandardizationStep(StandardizationStepEvent event) {
        try {
            if (event.getCurrentStep() != StandardizationSubStepEnum.FINAL_STANDARDIZATION) {
                return;
            }

            log.info("[标准化步骤] 开始处理最终标准化步骤: skuId={}, platformCode={}", event.getDynamicStandardProduct().getStringField("skuId"), event.getPlatformCode());
            log.debug("[标准化步骤] 最终标准化步骤前的数据: {}", event.getDynamicStandardProduct().getFields());

            // 执行最终标准化规则组
            DynamicStandardProduct processedData = dataStandardizationService.executeStandardizationStepWithJson(StandardizationSubStepEnum.FINAL_STANDARDIZATION, event.getOriginalDataMap(), event.getDynamicStandardProduct());

            log.debug("[标准化步骤] 最终标准化步骤后的数据: {}", processedData.getFields());
            log.info("[标准化步骤] 最终标准化步骤处理完成: skuId={}, platformCode={}", event.getDynamicStandardProduct().getStringField("skuId"), event.getPlatformCode());

            // 保存处理结果
            dataStandardizationService.saveProcessedData(event.getDynamicStandardProduct().getStringField("skuId"), processedData, StandardizationSubStepEnum.FINAL_STANDARDIZATION);

            // 发布标准化完成事件
            StandardizationCompletedEvent completedEvent = new StandardizationCompletedEvent(this, event.getDynamicStandardProduct().getStringField("skuId"), event.getPlatformCode(), event.getOriginalDataMap(), processedData, event.getDataId());

            eventPublisher.publishEvent(completedEvent);
            // 记录处理状态
            dataStandardizationService.recordStepCompletion(event.getDynamicStandardProduct().getStringField("skuId"), StandardizationSubStepEnum.FINAL_STANDARDIZATION, processedData, true);

        } catch (Exception e) {
            log.error("[标准化步骤] 最终标准化步骤发生异常，skuId={}, platformCode={}", event.getDynamicStandardProduct().getStringField("skuId"), event.getPlatformCode(), e);
            dataStandardizationService.recordStepCompletion(event.getDynamicStandardProduct().getStringField("skuId"), StandardizationSubStepEnum.FINAL_STANDARDIZATION, null, false);
        }
    }
}
