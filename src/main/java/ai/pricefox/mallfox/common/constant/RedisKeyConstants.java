package ai.pricefox.mallfox.common.constant;

/**
 * Redis 缓存键常量
 * 命名规范：
 * 1. 模块名:业务:具体操作
 * 2. 所有键使用小写字母，单词之间用冒号分隔
 * 3. 过期时间常量命名为 XXX_TIMEOUT，单位为秒
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
public interface RedisKeyConstants {

    // ========== 系统模块 ==========

    /**
     * 系统默认缓存过期时间：1小时
     */
    long DEFAULT_TIMEOUT = 3600;

    /**
     * 系统默认长期缓存过期时间：24小时
     */
    long DEFAULT_LONG_TIMEOUT = 86400;

    // ========== 用户模块 ==========

    /**
     * 用户令牌前缀
     */
    String USER_TOKEN = "user:token:";

    /**
     * 用户ID关联令牌前缀
     */
    String USER_ID_TOKEN = "user:id_token:";

    /**
     * 刷新令牌前缀
     */
    String USER_REFRESH_TOKEN = "user:refresh_token:";

    /**
     * 用户令牌过期时间：2小时
     */
    long USER_TOKEN_TIMEOUT = 7200;

    /**
     * 刷新令牌过期时间：30天
     */
    long USER_REFRESH_TOKEN_TIMEOUT = 2592000;

    /**
     * 用户收藏列表前缀
     */
    String USER_WISHLIST = "user:wishlist:";

    /**
     * 用户收藏列表过期时间：1小时
     */
    long USER_WISHLIST_TIMEOUT = 3600;

    // ========== 管理员模块 ==========

    /**
     * 管理员令牌前缀
     */
    String ADMIN_TOKEN = "admin:token:";

    /**
     * 管理员ID关联令牌前缀
     */
    String ADMIN_ID_TOKEN = "admin:id_token:";

    /**
     * 管理员刷新令牌前缀
     */
    String ADMIN_REFRESH_TOKEN = "admin:refresh_token:";

    /**
     * 管理员令牌过期时间：2小时
     */
    long ADMIN_TOKEN_TIMEOUT = 7200;

    // ========== 商品模块 ==========

    /**
     * 商品详情缓存前缀
     */
    String PRODUCT_DETAIL = "product:detail:";

    /**
     * 商品详情缓存过期时间：24小时
     */
    long PRODUCT_DETAIL_TIMEOUT = 86400;

    /**
     * 商品变体缓存前缀
     */
    String PRODUCT_VARIANTS = "product:variants:";

    /**
     * 商品变体缓存过期时间：24小时
     */
    long PRODUCT_VARIANTS_TIMEOUT = 86400;

    /**
     * 商品价格历史缓存前缀
     */
    String PRODUCT_PRICE_HISTORY = "product:price_history:";

    /**
     * 商品价格历史缓存过期时间：12小时
     */
    long PRODUCT_PRICE_HISTORY_TIMEOUT = 43200;

    /**
     * 商品标记缓存前缀 - Offers
     */
    String PRODUCT_CALIBRATION_OFFERS = "product:calibration:offers:";

    /**
     * 商品标记缓存前缀 - Simplify
     */
    String PRODUCT_CALIBRATION_SIMPLIFY = "product:calibration:simplify:";

    /**
     * 商品标记缓存过期时间：1小时
     */
    long PRODUCT_CALIBRATION_TIMEOUT = 3600;

    // ========== 字段追踪模块 ==========

    /**
     * 字段来源缓存前缀
     */
    String FIELD_SOURCES = "field:sources:";

    /**
     * 字段来源缓存过期时间：12小时
     */
    long FIELD_SOURCES_TIMEOUT = 43200;

    // ========== 集成模块 ==========

    /**
     * eBay OAuth令牌前缀
     */
    String EBAY_OAUTH_TOKEN = "ebay:oauth:token";

    /**
     * eBay OAuth刷新令牌前缀
     */
    String EBAY_OAUTH_REFRESH_TOKEN = "ebay:oauth:refresh_token";

    /**
     * eBay同步状态前缀
     */
    String EBAY_SYNC_STATUS = "ebay:sync:status";

    /**
     * eBay待处理模型缓存前缀
     */
    String EBAY_SYNC_PENDING_MODELS = "ebay:sync:pending_models";

    /**
     * eBay已同步商品缓存前缀
     */
    String EBAY_SYNC_SYNCED_ITEMS = "ebay:sync:synced_items";

    /**
     * eBay同步缓存过期时间：24小时
     */
    long EBAY_SYNC_TIMEOUT = 86400;

    // ========== 验证码模块 ==========

    /**
     * 邮箱验证码前缀
     */
    String EMAIL_CODE = "verify:email_code:";

    /**
     * 短信验证码前缀
     */
    String SMS_CODE = "verify:sms_code:";

    /**
     * 发送频率限制前缀
     */
    String SEND_FREQUENCY = "verify:send_freq:";

    /**
     * 验证码过期时间：5分钟
     */
    long VERIFY_CODE_TIMEOUT = 300;

    /**
     * 发送频率限制时间：1分钟
     */
    long SEND_FREQUENCY_TIMEOUT = 60;

    // ========== 数据标准化模块 ==========

    /**
     * 无效值模式缓存键
     */
    String INVALID_VALUE_PATTERNS = "standard:invalid_value_patterns";

    /**
     * 归一化别名缓存键
     */
    String NORMALIZATION_ALIAS = "standard:normalization_alias";

    /**
     * 归一化值库缓存键
     */
    String NORMALIZATION_LIBRARY = "standard:normalization_library";

    /**
     * 归一化规则缓存键
     */
    String NORMALIZATION_RULES = "standard:normalization_rules";

    /**
     * 平台分类映射缓存键
     */
    String PLATFORM_CATEGORY = "standard:platform_category";
    /**
     * 平台分类映射缓存键
     */
    String PLATFORM_CATEGORY_MAPPING = "standard:platform_category_mapping";
    /**
     * 标准分类缓存键
     */
    String STANDARD_CATEGORY = "standard:category";
    /**
     * 标准分类映射缓存键
     */
    String STANDARD_CATEGORY_MAPPING = "standard:category:mapping";
    /**
     * 标准属性缓存键
     */
    String STANDARD_ATTRIBUTE = "standard:attribute";
    /**
     * 标准属性值缓存键
     */
    String STANDARD_ATTRIBUTE_MAPPING = "standard:attribute:mapping";
    /**
     * 标准属性值缓存键
     */
    String STANDARD_ATTRIBUTE_VALUE = "standard:attribute:value";
    /**
     * 标准属性值映射缓存键
     */
    String STANDARD_ATTRIBUTE_VALUE_MAPPING = "standard:attribute:value:mapping";
    /**
     * 第三方平台缓存键
     */
    String TRIPARTITE_PLATFORM = "standard:tripartite_platform";

    /**
     * 数据标准化缓存过期时间：1小时
     */
    long STANDARD_DATA_TIMEOUT = 3600;

    // ========== 平台数据模块 ==========
    String PLATFORM_CODE_BY_NAME = "platform:code:name:";

    String PLATFORM_NAME_BY_CODE = "platform:name:code:";

}
