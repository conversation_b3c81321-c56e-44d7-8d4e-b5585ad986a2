package ai.pricefox.mallfox.common.exception.enums;

import ai.pricefox.mallfox.common.exception.ErrorCode;

/**
 * System 错误码枚举类
 * <p>
 * system 系统，使用 1-002-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== 字典模块 1-001-001-000 ==========
    ErrorCode DICT_NOT_EXIST = new ErrorCode(1_001_001_000, "字典不存在");
    ErrorCode DICT_VALUE_NOT_EXIST = new ErrorCode(1_001_001_001, "字典项不存在");

    // ========== 用户模块 1-001-002-000 ==========
    ErrorCode USER_NOT_EXIST = new ErrorCode(1_001_002_000, "用户不存在");

    // ========== 品牌模块 1-001-003-000 ==========
    ErrorCode BRAND_NOT_EXIST = new ErrorCode(1_001_003_000, "品牌不存在");
    ErrorCode BRAND_INFO_NOT_EXIST = new ErrorCode(1_001_003_001, "品牌信息不存在");
    ErrorCode BRAND_INFO_NAME_EXISTS = new ErrorCode(1_001_003_002, "品牌名称已存在");

    // ========== 商品模块 1-001-004-000 ==========
    ErrorCode PRODUCT_NOT_EXIST = new ErrorCode(1_001_004_000, "商品不存在");
    ErrorCode PRODUCT_SKU_NOT_EXIST = new ErrorCode(1_001_004_001, "商品skub不存在");



    // ========== 分类模块 1-001-005-000 ==========
    ErrorCode CATEGORY_NOT_EXIST = new ErrorCode(1_001_005_000, "分类不存在");
    ErrorCode CATEGORY_INFO_NOT_EXIST = new ErrorCode(1_001_005_001, "分类信息不存在");
    ErrorCode CATEGORY_INFO_NAME_EXISTS = new ErrorCode(1_001_005_002, "同已有类目名称重复");
    ErrorCode CATEGORY_INFO_PARENT_NOT_EXIST = new ErrorCode(1_001_005_003, "父分类不存在");
    ErrorCode CATEGORY_INFO_PARENT_ERROR = new ErrorCode(1_001_005_004, "不能设置自己为父分类");
    ErrorCode CATEGORY_INFO_HAS_CHILDREN = new ErrorCode(1_001_005_005, "请先确认类目下无子级类目后再进行删除");
    ErrorCode CATEGORY_HAS_ATTRIBUTES = new ErrorCode(1_001_005_006, "该分类下存在属性，无法删除");
    ErrorCode CATEGORY_HAS_PLATFORM_MAPPING = new ErrorCode(1_001_005_007, "该分类存在平台映射关系，无法删除");

    // ========== 规格模块 1-001-006-000 ==========
    ErrorCode ATTRIBUTE_NOT_EXIST = new ErrorCode(1_001_006_000, "规格不存在");

    // ========== 数据模块 1-001-007-000 ==========
    ErrorCode DATA_OFFERS_NOT_EXIST = new ErrorCode(1_001_007_000, "商品报价数据不存在");
    ErrorCode DATA_REVIEWS_NOT_EXIST = new ErrorCode(1_001_007_001, "商品评论数据不存在");
    ErrorCode DATA_SIMPLIFY_NOT_EXIST = new ErrorCode(1_001_007_002, "商品简化数据不存在");
    ErrorCode DATA_PRODUCT_LIMIT = new ErrorCode(1_001_007_003, "商品输入数据大于100条");
    ErrorCode DATA_PRODUCT_NOT_EXIST = new ErrorCode(1_001_007_004, "商品输入数据不存在");
    ErrorCode DATA_MODEL_PROCESS_FAILED = new ErrorCode(1_001_007_005, "商品型号处理失败");
    ErrorCode DATA_MODEL_NO_DATA_TO_PROCESS = new ErrorCode(1_001_007_006, "没有需要处理的商品型号数据");
    ErrorCode DATA_MODEL_MERGE_FAILED = new ErrorCode(1_001_007_007, "商品型号合并失败");
    ErrorCode DATA_MODEL_NO_DATA_TO_MERGE = new ErrorCode(1_001_007_008, "没有需要合并的商品型号数据");


    // ========== 文件模块 1-001-008-000 ==========
    ErrorCode FILE_UPLOAD_FAILED = new ErrorCode(1_001_008_000, "文件上传失败");

    // ========== 认证模块 1-001-009-000 ==========
    ErrorCode AUTH_LOGIN_BAD_CREDENTIALS = new ErrorCode(1_001_009_000, "登录失败，账号密码不正确");
    ErrorCode AUTH_LOGIN_USER_DISABLED = new ErrorCode(1_001_009_001, "登录失败，账号被禁用");
    ErrorCode AUTH_LOGIN_CAPTCHA_CODE_ERROR = new ErrorCode(1_001_009_002, "验证码不正确");
    ErrorCode AUTH_LOGIN_FAIL_UNKNOWN = new ErrorCode(1_001_009_003, "登录失败");
    ErrorCode AUTH_TOKEN_EXPIRED = new ErrorCode(1_001_009_004, "Token 已过期");
    ErrorCode AUTH_TOKEN_INVALID = new ErrorCode(1_001_009_005, "Token 无效");
    ErrorCode AUTH_TOKEN_NOT_FOUND = new ErrorCode(1_001_009_006, "Token 不存在");
    ErrorCode AUTH_PERMISSION_DENIED = new ErrorCode(1_001_009_007, "权限不足");
    ErrorCode AUTH_MOBILE_NOT_EXISTS = new ErrorCode(1_001_009_008, "手机号不存在");
    ErrorCode AUTH_EMAIL_NOT_EXISTS = new ErrorCode(1_001_009_009, "邮箱不存在");
    ErrorCode AUTH_CODE_SEND_TOO_FAST = new ErrorCode(1_001_009_010, "短信发送过于频繁");
    ErrorCode AUTH_CODE_NOT_FOUND = new ErrorCode(1_001_009_011, "验证码不存在");
    ErrorCode AUTH_CODE_EXPIRED = new ErrorCode(1_001_009_012, "验证码已过期");
    ErrorCode AUTH_MOBILE_CODE_NOT_CORRECT = new ErrorCode(1_001_009_013, "手机验证码不正确");
    ErrorCode AUTH_EMAIL_CODE_NOT_CORRECT = new ErrorCode(1_001_009_014, "邮箱验证码不正确");
    ErrorCode AUTH_RESET_TOKEN_INVALID = new ErrorCode(1_001_009_015, "重置密码令牌无效");
    ErrorCode AUTH_RESET_TOKEN_EXPIRED = new ErrorCode(1_001_009_016, "重置密码令牌已过期");
    ErrorCode AUTH_USER_ALREADY_EXISTS = new ErrorCode(1_001_009_017, "用户已存在");
    ErrorCode AUTH_REGISTER_FAIL = new ErrorCode(1_001_009_018, "注册失败");
    ErrorCode AUTH_PHONE_ALREADY_EXISTS = new ErrorCode(1_001_009_019, "手机号已存在");
    ErrorCode AUTH_EMAIL_ALREADY_EXISTS = new ErrorCode(1_001_009_020, "邮箱已存在");
    ErrorCode AUTH_MOBILE_CODE_SEND_FAIL = new ErrorCode(1_001_009_021, "短信验证码发送失败");
    ErrorCode AUTH_EMAIL_CODE_SEND_FAIL = new ErrorCode(1_001_009_022, "邮箱验证码发送失败");
    ErrorCode AUTH_EMAIL_SEND_FAIL = new ErrorCode(1_001_009_023, "邮件发送失败");

    // 后台用户相关错误码
    ErrorCode AUTH_ADMIN_USER_NOT_EXIST = new ErrorCode(1_001_009_024, "后台用户不存在");
    ErrorCode AUTH_ADMIN_USER_DISABLED = new ErrorCode(1_001_009_025, "后台用户已被禁用");
    ErrorCode AUTH_ADMIN_LOGIN_FAIL = new ErrorCode(1_001_009_026, "后台用户登录失败");
    ErrorCode AUTH_ADMIN_TOKEN_INVALID = new ErrorCode(1_001_009_027, "后台用户Token无效");
    ErrorCode AUTH_ADMIN_PERMISSION_DENIED = new ErrorCode(1_001_009_028, "后台用户权限不足");
    
    // 第三方登录相关错误码
    ErrorCode AUTH_THIRD_LOGIN_BIND_FAIL = new ErrorCode(1_001_009_029, "绑定第三方账号失败");
    ErrorCode AUTH_THIRD_LOGIN_BIND_EXIST = new ErrorCode(1_001_009_030, "该第三方账号已被绑定");
    ErrorCode AUTH_THIRD_LOGIN_UNBIND_FAIL = new ErrorCode(1_001_009_031, "解绑第三方账号失败");
    ErrorCode AUTH_THIRD_LOGIN_NOT_BIND = new ErrorCode(1_001_009_032, "未绑定该第三方账号");

}
