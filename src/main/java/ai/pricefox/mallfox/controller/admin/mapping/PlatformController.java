package ai.pricefox.mallfox.controller.admin.mapping;

import ai.pricefox.mallfox.model.vo.mapping.platform.*;
import ai.pricefox.mallfox.service.mapping.PlatformService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 平台管理
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@RestController
@AllArgsConstructor
@RequestMapping("/api/admin/platforms")
@Tag(name = "管理后台-平台管理", description = "第三方平台管理相关接口")
public class PlatformController {

    @Autowired
    private  PlatformService platformService;

    /**
     * 查询平台列表(不分页)
     *
     * @return 平台列表
     */
    @Operation(summary = "查询平台列表", description = "获取所有平台列表，不分页")
    @GetMapping("/list")
    public CommonResult<List<PlatformRespVO>> getPlatformList() {
        return platformService.getPlatformList();
    }

    /**
     * 创建新平台
     *
     * @param reqVO 创建请求
     * @return 创建结果
     */
    @Operation(summary = "创建平台", description = "创建新的第三方平台")
    @PostMapping("/add")
    public CommonResult<PlatformRespVO> addPlatform(@Valid @RequestBody PlatformCreateReqVO reqVO) {
        return platformService.createPlatform(reqVO);
    }

    /**
     * 更新平台信息
     *
     * @param reqVO 更新请求
     * @return 更新结果
     */
    @Operation(summary = "更新平台", description = "更新平台信息")
    @PostMapping("/edit")
    public CommonResult<PlatformRespVO> editPlatform(@Valid @RequestBody PlatformUpdateReqVO reqVO) {
        return platformService.updatePlatform(reqVO);
    }

    /**
     * 删除平台
     *
     * @param id 平台ID
     * @return 删除结果
     */
    @Operation(summary = "删除平台", description = "根据ID删除平台")
    @DeleteMapping("/{id}")
    public CommonResult<Boolean> deletePlatform(
            @Parameter(description = "平台ID", example = "1") @PathVariable Integer id) {
        return platformService.deletePlatform(id);
    }

    /**
     * 平台查询详情
     *
     * @param id 平台ID
     * @return 平台详情
     */
    @Operation(summary = "查询平台详情", description = "根据ID查询平台详细信息")
    @GetMapping("/get")
    public CommonResult<PlatformRespVO> getPlatform(
            @Parameter(description = "平台ID", example = "1") @RequestParam Integer id) {
        return platformService.getPlatformById(id);
    }
}