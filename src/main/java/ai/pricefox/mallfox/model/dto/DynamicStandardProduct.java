package ai.pricefox.mallfox.model.dto;

import ai.pricefox.mallfox.domain.standard.PlatformFieldMapping;
import ai.pricefox.mallfox.domain.standard.StandardField;
import ai.pricefox.mallfox.enums.DataChannelEnum;
import ai.pricefox.mallfox.enums.ProductPlatformEnum;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 动态标准产品类
 * 使用Map存储所有标准字段的值，键为StandardField中的field_name_en
 */
@Data
public class DynamicStandardProduct {
    /**
     * 标准数据
     */
    private Map<String, StandardField> fields = new HashMap<>();

    /**
     * 源数据
     */
    private Map<String, Object> originData = new HashMap<>();

    /**
     * 当前数据
     */
    private Map<String, Object> currentData = new HashMap<>();
    /**
     * 平台字段对应关系
     */
    private List<PlatformFieldMapping> fieldMappingList;
    /**
     * 平台名称
     */
    private ProductPlatformEnum platformName;

    /**
     * 平台代码
     */
    private String platformCode;

    /**
     * 数据来源
     */
    private DataChannelEnum sourceType;

    /**
     * 商品标识符
     */
    private String productIdentifier;

    /**
     * 源数据ID
     */
    private String dataId;

    /**
     * 设置字段值
     *
     * @param fieldName StandardField.field_name_en
     * @param value     字段值
     */
    public void setField(String fieldName, StandardField value) {
        fields.put(fieldName, value);
    }


    /**
     * 获取字符串类型的字段值
     *
     * @param fieldName StandardField.field_name_en
     * @return 字符串字段值
     */
    public String getStringField(String fieldName) {
        Object value = fields.get(fieldName);
        return value != null ? value.toString() : null;
    }

    /**
     * 检查是否存在某个字段
     *
     * @param fieldName StandardField.field_name_en
     * @return 是否存在
     */
    public boolean hasField(String fieldName) {
        return fields.containsKey(fieldName);
    }

    /**
     * 检查字段是否为空
     *
     * @param fieldName StandardField.field_name_en
     * @return 字段是否为空
     */
    public boolean isFieldEmpty(String fieldName) {
        Object value = fields.get(fieldName);
        if (value == null) {
            return true;
        }
        if (value instanceof String) {
            return ((String) value).trim().isEmpty();
        }
        return false;
    }

    /**
     * 检查是否存在指定字段且不为空
     *
     * @param fieldName 字段名
     * @return 是否存在且不为空
     */
    public boolean hasFieldWithValue(String fieldName) {
        return hasField(fieldName) && !isFieldEmpty(fieldName);
    }
}
