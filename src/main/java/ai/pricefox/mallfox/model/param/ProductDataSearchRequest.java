package ai.pricefox.mallfox.model.param;


import ai.pricefox.mallfox.enums.DataChannelEnum;
import ai.pricefox.mallfox.enums.ProductPlatformEnum;
import ai.pricefox.mallfox.enums.StandardizationSubStepEnum;
import ai.pricefox.mallfox.utils.FieldMappingUtil;
import ai.pricefox.mallfox.vo.base.PageParam;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc 产品数据查询列表
 * @since 2025/6/24
 */
@Data
public class ProductDataSearchRequest extends PageParam {

    /**
     * id
     */
    @Schema(description = "id", example = "1")
    private Long id;

    /**
     * 品牌
     */
    @Schema(description = "品牌", example = "Apple")
    private String brand;

    /**
     * 型号a
     */
    @Schema(description = "型号", example = "iPhone 13")
    private String model;

    /**
     * 系列
     */
    @Schema(description = "系列", example = "13")
    private String series;

    /**
     * SPU ID
     */
    @Schema(description = "SPU ID", example = "PP00012012")
    private String spuId;

    /**
     * SKU ID
     */
    @Schema(description = "SKU ID", example = "PK0122300122")
    private String skuId;

    /**
     * 来源平台 ebay Amazon best buy
     */
    @Schema(description = "来源平台", example = "ebay")
    private ProductPlatformEnum sourcePlatform;

    /**
     * 数据渠道
     */
    private DataChannelEnum channel;
    /**
     * 是否匹配
     */
    private Boolean isMatch;
    /**
     * 步骤
     */
    private StandardizationSubStepEnum type;

    /**
     * 标题
     */
    @Schema(description = "标题", example = "Apple iPhone 13 Pro 128GB Deep Purple")
    private String title;

    /**
     * 颜色
     */
    private String color;

    /**
     * 内存
     */
    @Schema(description = "存储", example = "128")
    private String storage;

    /**
     * 服务提供商
     */
    @Schema(description = "服务提供商", example = "Amazon")
    private String serviceProvider;

    /**
     * 新旧程度
     */
    private String conditionNew;


    /**
     * UPC码
     */
    private String upcCode;

    /**
     * 排序字段（驼峰格式，如：spuId, modelYear, createTime）
     */
    @Schema(description = "排序字段", example = "spuId")
    private String sortField;

    /**
     * 排序方向（asc/desc）
     */
    @Schema(description = "排序方向", example = "asc", allowableValues = {"asc", "desc"})
    private String sortOrder = "asc";

    /**
     * 获取数据库排序字段（转换后的）
     */
    @JsonIgnore
    public String getDbSortField() {
        return FieldMappingUtil.convertToDbField(this.sortField);
    }

    /**
     * 获取适合GROUP BY查询的数据库排序字段（转换后的）
     */
    @JsonIgnore
    public String getDbSortFieldForGroupBy() {
        return FieldMappingUtil.convertToDbFieldForGroupBy(this.sortField);
    }

    /**
     * 获取数据库排序方向（转换后的）
     */
    @JsonIgnore
    public String getDbSortOrder() {
        return FieldMappingUtil.validateSortOrder(this.sortOrder);
    }
}
