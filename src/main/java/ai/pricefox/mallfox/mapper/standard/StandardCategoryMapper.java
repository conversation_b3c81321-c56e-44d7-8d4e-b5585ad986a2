package ai.pricefox.mallfox.mapper.standard;

import ai.pricefox.mallfox.domain.standard.StandardCategory;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 标准品类库Mapper接口
 */
@Mapper
public interface StandardCategoryMapper extends BaseMapper<StandardCategory> {

    /**
     * 检查是否有子分类
     */
    default long countChildrenByParentId(Long parentId) {
        LambdaQueryWrapper<StandardCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardCategory::getParent, parentId);
        return selectCount(wrapper);
    }

    /**
     * 根据英文名称检查重复
     */
    default StandardCategory findByCategoryNameEn(String categoryNameEn) {
        LambdaQueryWrapper<StandardCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardCategory::getCategoryNameEn, categoryNameEn);
        return selectOne(wrapper);
    }

    /**
     * 根据编码检查重复
     */
    default StandardCategory findByCategoryCode(String categoryCode) {
        LambdaQueryWrapper<StandardCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardCategory::getCategoryCode, categoryCode);
        return selectOne(wrapper);
    }

    /**
     * 获取树形结构数据（按英文名称排序）
     */
    default List<StandardCategory> selectTreeList(String categoryCode, String categoryNameEn, String categoryNameCn) {
        LambdaQueryWrapper<StandardCategory> wrapper = new LambdaQueryWrapper<>();

        // 条件筛选
        if (StringUtils.hasText(categoryCode)) {
            wrapper.eq(StandardCategory::getCategoryCode, categoryCode);
        }
        if (StringUtils.hasText(categoryNameEn)) {
            wrapper.like(StandardCategory::getCategoryNameEn, categoryNameEn);
        }
        if (StringUtils.hasText(categoryNameCn)) {
            wrapper.like(StandardCategory::getCategoryNameCn, categoryNameCn);
        }

        // 排序：先按level，再按英文名称
        wrapper.orderByAsc(StandardCategory::getLevel)
               .orderByAsc(StandardCategory::getCategoryNameEn);

        return selectList(wrapper);
    }

    /**
     * 分页查询列表
     */
    default IPage<StandardCategory> selectPageList(Page<StandardCategory> page, String categoryCode, String categoryNameEn, String categoryNameCn) {
        LambdaQueryWrapper<StandardCategory> wrapper = new LambdaQueryWrapper<>();

        if (StringUtils.hasText(categoryCode)) {
            wrapper.eq(StandardCategory::getCategoryCode, categoryCode);
        }
        if (StringUtils.hasText(categoryNameEn)) {
            wrapper.like(StandardCategory::getCategoryNameEn, categoryNameEn);
        }
        if (StringUtils.hasText(categoryNameCn)) {
            wrapper.like(StandardCategory::getCategoryNameCn, categoryNameCn);
        }

        wrapper.orderByAsc(StandardCategory::getLevel)
               .orderByAsc(StandardCategory::getCategoryNameEn);

        return selectPage(page, wrapper);
    }

}




