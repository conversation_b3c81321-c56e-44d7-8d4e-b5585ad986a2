package ai.pricefox.mallfox.mapper.standard;

import ai.pricefox.mallfox.domain.standard.StandardAttribute;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 标准属性库Mapper接口
 */
@Mapper
public interface StandardAttributeMapper extends BaseMapper<StandardAttribute> {

    /**
     * 根据分类编码统计属性数量
     */
    default long countByCategoryCode(String categoryCode) {
        LambdaQueryWrapper<StandardAttribute> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardAttribute::getStandardCategoryCode, categoryCode)
               .eq(StandardAttribute::getDeleted, false);
        return selectCount(wrapper);
    }

    /**
     * 根据分类编码查询属性列表
     */
    default List<StandardAttribute> selectByCategoryCode(String categoryCode) {
        LambdaQueryWrapper<StandardAttribute> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardAttribute::getStandardCategoryCode, categoryCode)
               .eq(StandardAttribute::getDeleted, false);
        return selectList(wrapper);
    }

    /**
     * 根据属性编码查询属性
     */
    default StandardAttribute selectByAttributeCode(String attributeCode) {
        LambdaQueryWrapper<StandardAttribute> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardAttribute::getAttributeCode, attributeCode)
               .eq(StandardAttribute::getDeleted, false);
        return selectOne(wrapper);
    }
}
