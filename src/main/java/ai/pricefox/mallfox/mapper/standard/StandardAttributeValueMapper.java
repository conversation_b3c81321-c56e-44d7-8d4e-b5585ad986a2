package ai.pricefox.mallfox.mapper.standard;

import ai.pricefox.mallfox.domain.standard.StandardAttributeValue;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 标准属性值Mapper接口
 */
@Mapper
public interface StandardAttributeValueMapper extends BaseMapper<StandardAttributeValue> {

    /**
     * 根据属性编码统计属性值数量
     */
    default long countByAttributeCode(String attributeCode) {
        LambdaQueryWrapper<StandardAttributeValue> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardAttributeValue::getAttributeCode, attributeCode)
               .eq(StandardAttributeValue::getDeleted, false);
        return selectCount(wrapper);
    }

    /**
     * 根据属性编码查询属性值列表
     */
    default List<StandardAttributeValue> selectByAttributeCode(String attributeCode) {
        LambdaQueryWrapper<StandardAttributeValue> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardAttributeValue::getAttributeCode, attributeCode)
               .eq(StandardAttributeValue::getDeleted, false);
        return selectList(wrapper);
    }
}
