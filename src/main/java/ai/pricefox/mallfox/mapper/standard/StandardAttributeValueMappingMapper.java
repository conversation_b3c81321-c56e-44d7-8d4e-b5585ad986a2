package ai.pricefox.mallfox.mapper.standard;

import ai.pricefox.mallfox.domain.standard.StandardAttributeValueMapping;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 映射属性值Mapper接口
 */
@Mapper
public interface StandardAttributeValueMappingMapper extends BaseMapper<StandardAttributeValueMapping> {

    /**
     * 根据属性编码统计属性值映射数量
     */
    default long countByAttributeCode(String attributeCode) {
        LambdaQueryWrapper<StandardAttributeValueMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardAttributeValueMapping::getAttributeCode, attributeCode)
               .eq(StandardAttributeValueMapping::getDeleted, false);
        return selectCount(wrapper);
    }

    /**
     * 根据属性编码查询属性值映射列表
     */
    default List<StandardAttributeValueMapping> selectByAttributeCode(String attributeCode) {
        LambdaQueryWrapper<StandardAttributeValueMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardAttributeValueMapping::getAttributeCode, attributeCode)
               .eq(StandardAttributeValueMapping::getDeleted, false);
        return selectList(wrapper);
    }
}
