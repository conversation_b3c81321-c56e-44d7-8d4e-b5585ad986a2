package ai.pricefox.mallfox.mapper.standard;

import ai.pricefox.mallfox.domain.standard.StandardAttributeMapping;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 映射属性名Mapper接口
 */
@Mapper
public interface StandardAttributeMappingMapper extends BaseMapper<StandardAttributeMapping> {

    /**
     * 根据属性编码统计映射数量
     */
    default long countByAttributeCode(String attributeCode) {
        LambdaQueryWrapper<StandardAttributeMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardAttributeMapping::getAttributeCode, attributeCode)
               .eq(StandardAttributeMapping::getDeleted, false);
        return selectCount(wrapper);
    }

    /**
     * 根据属性编码查询映射列表
     */
    default List<StandardAttributeMapping> selectByAttributeCode(String attributeCode) {
        LambdaQueryWrapper<StandardAttributeMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardAttributeMapping::getAttributeCode, attributeCode)
               .eq(StandardAttributeMapping::getDeleted, false);
        return selectList(wrapper);
    }
}
