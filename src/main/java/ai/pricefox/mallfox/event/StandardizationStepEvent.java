package ai.pricefox.mallfox.event;

import ai.pricefox.mallfox.enums.StandardizationSubStepEnum;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.Map;

/**
 * 标准化步骤事件
 * 用于标准化流程中传递基础数据
 * 每个步骤可独立发布和处理
 */
@Getter
@EqualsAndHashCode(callSuper = true)
public class StandardizationStepEvent extends ApplicationEvent {
    /**
     * 平台编码
     */
    private final String platformCode;

    private final String skuId;

    /**
     * 当前步骤标识
     */
    private final StandardizationSubStepEnum currentStep;

    /**
     * 当前处理的数据
     */
    private final DynamicStandardProduct dynamicStandardProduct;

    /**
     * 原始数据存储ID
     */
    private final String dataId;

    /**
     * 原始数据映射（JSON格式）
     */
    private final Map<String, Object> originalDataMap;

    /**
     * 创建标准化步骤事件
     *
     * @param source                 事件源对象
     * @param platformCode           平台编码
     * @param currentStep            当前处理步骤
     * @param originalDataMap        原始数据映射
     * @param dynamicStandardProduct 当前处理数据
     * @param dataId                 原始数据存储ID（可为空）
     */
    public StandardizationStepEvent(Object source, String platformCode, String skuId, StandardizationSubStepEnum currentStep, DynamicStandardProduct dynamicStandardProduct, String dataId, Map<String, Object> originalDataMap) {
        super(source);
        this.platformCode = platformCode;
        this.skuId = skuId;
        this.currentStep = currentStep;
        this.dynamicStandardProduct = dynamicStandardProduct;
        this.dataId = dataId;
        this.originalDataMap = originalDataMap;
    }

}