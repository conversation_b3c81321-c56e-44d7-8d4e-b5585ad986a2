package ai.pricefox.mallfox.event;

import ai.pricefox.mallfox.enums.StandardizationSubStepEnum;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.Map;

/**
 * 标准化步骤事件
 * 用于标准化流程中传递基础数据
 * 每个步骤可独立发布和处理
 */
@Getter
@EqualsAndHashCode(callSuper = true)
public class StandardizationStepEvent extends ApplicationEvent {
    /**
     * 平台编码
     */
    private final String platformCode;
    /**
     * skuId
     */
    private final String skuId;
    /**
     * 当前步骤标识
     */
    private final StandardizationSubStepEnum currentStep;
    /**
     * 过程数据
     */
    private final DynamicStandardProduct dynamicStandardProduct;

    /**
     * 创建标准化步骤事件
     *
     * @param source                 事件源
     * @param platformCode           平台编码
     * @param skuId                  skuId
     * @param currentStep            当前步骤标识
     * @param dynamicStandardProduct 动态标准产品
     */
    public StandardizationStepEvent(Object source, String platformCode, String skuId, StandardizationSubStepEnum currentStep, DynamicStandardProduct dynamicStandardProduct) {
        super(source);
        this.platformCode = platformCode;
        this.skuId = skuId;
        this.currentStep = currentStep;
        this.dynamicStandardProduct = dynamicStandardProduct;
    }

}