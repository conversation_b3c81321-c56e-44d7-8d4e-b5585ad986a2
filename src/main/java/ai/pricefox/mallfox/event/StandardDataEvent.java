package ai.pricefox.mallfox.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 标准化处理数据事件
 */
@Getter
public class StandardDataEvent extends ApplicationEvent {
    /**
     * 原始数据Json
     */
    private final String originalData;

    public StandardDataEvent(Object source, String originalData) {
        super(source);
        this.originalData = originalData;
    }
}
