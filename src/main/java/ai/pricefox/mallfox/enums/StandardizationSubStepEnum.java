package ai.pricefox.mallfox.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 标准化子步骤枚举
 * 用于精细化控制标准化流程的每个阶段
 */
@Getter
@AllArgsConstructor
public enum StandardizationSubStepEnum {
    MARGE_DATA("marge_data", "数据合并"),
    FIELD_MAPPING("field_mapping", "字段映射"),
    CATEGORY_STANDARDIZATION("category_standardization", "品类标准化"),
    BRAND_STANDARDIZATION("brand_standardization", "品牌标准化"),
    MODEL_STANDARDIZATION("model_standardization", "型号标准化"),
    OTHER_STANDARDIZATION("other_standardization", "其他属性标准化"),
    INDISTINGUISHABLE_PROCESSING("indistinguishable_processing", "无差异处理"),
    INVALID_VALUE_PROCESSING("invalid_value_processing", "无效值处理"),
    FINAL_STANDARDIZATION("final_standardization", "最终标准化");

    private final String code;
    private final String description;
}