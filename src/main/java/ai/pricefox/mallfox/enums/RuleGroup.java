package ai.pricefox.mallfox.enums;

import lombok.Getter;

/**
 * 规则组枚举，用于定义不同步骤对应的规则名称
 * 注释格式说明：
 * - 规则名称后用括号标注所属文件
 * - 简要描述规则核心功能
 * - 标明规则主要处理逻辑或触发条件
 */
@Getter
public enum RuleGroup {
    FIELD_MAPPING(new String[]{
            // 字段映射规则 (PlatformFieldMapping.drl)
            // 功能：将平台特定字段映射到标准字段
            // 特点：支持动态字段查找，自动处理大小写不匹配
            "Platform Field Mapping"
    }),

    CATEGORY(new String[]{
            // 品类标准化规则 (CategoryStandardization.drl)
            // 功能：将品类信息标准化为标准库定义格式
            // 特点：支持标准库匹配、标题提取、原始数据回退
            "Process Category Standardization",

            // 平台品类映射规则 (PlatformFieldMapping.drl)
            // 功能：处理平台特定品类代码到标准品类的映射
            // 特点：与PlatformFieldMappingRule配合使用
            "Platform Category Mapping Rule"}),

    BRAND(new String[]{
            // 品牌标准化规则 (BrandStandardization.drl)
            // 功能：将品牌名称标准化为标准库定义格式
            // 特点：支持多语言匹配，包含智能标题识别
            "Process Brand Standardization",

            // 标题品牌提取规则 (EntityStandardization.drl)
            // 功能：从商品标题自动提取品牌信息
            // 特点：内置常见品牌识别逻辑（Apple/Samsung等）
            "Process Entity Brand Standardization"}),

    MODEL(new String[]{
            // 型号标准化规则 (ModelStandardization.drl)
            // 功能：将型号信息标准化为标准库定义格式
            // 特点：支持复杂型号识别和正则提取
            "Process Model Standardization",

            // 标题型号提取规则 (ModelStandardization.drl)
            // 功能：从商品标题自动提取型号信息
            // 特点：结合标准库匹配和正则表达式提取
            "ModelExtractionFromTitleRule"}),

    COLOR(new String[]{
            // 颜色标准化规则 (ColorStandardization.drl)
            // 功能：将颜色信息标准化为标准库定义格式
            // 特点：支持多级颜色代码匹配
            "Process Color Standardization",

            // 标题颜色提取规则 (ColorStandardization.drl)
            // 功能：从商品标题自动提取颜色信息
            // 特点：支持模糊匹配和空格处理
            "ColorExtractionFromTitleRule"}),

    STORAGE(new String[]{
            // 存储标准化规则 (StorageStandardization.drl)
            // 功能：将存储信息标准化为标准库定义格式
            // 特点：支持多种存储单位识别
            "Process Storage Standardization",

            // 标题存储提取规则 (StorageStandardization.drl)
            // 功能：从商品标题自动提取存储信息
            // 特点：优先匹配标准库，再进行文本分析
            "StorageExtractionFromTitleRule"}),

    INDISTINGUISHABLE(new String[]{
            // 难以区分处理规则 (Normalization.drl)
            // 功能：处理需要特殊处理的模糊数据
            // 特点：结合标准库和正则表达式进行归一化
            "Process Indistinguishable",

            // 标准库归一化规则 (Normalization.drl)
            // 功能：基于标准库的动态归一化处理
            // 特点：支持品牌/颜色/存储/型号的多维度匹配
            "Dynamic Normalization Using Library",

            // 正则归一化规则 (RegexNormalization.drl)
            // 功能：使用正则表达式进行数据归一化
            // 特点：支持复杂模式匹配和替换
            "Regex Based Normalization",

            // 动态归一化规则 (Normalization.drl)
            // 功能：实现动态正则匹配和归一化处理
            // 特点：自动处理正则异常情况
            "Dynamic Normalization Rule"}),

    INVALID_VALUE(new String[]{
            // 无效值处理规则 (Normalization.drl)
            // 功能：处理非法或特殊格式的字段值
            // 特点：支持空值和特殊符号处理
            "Process Invalid Value",

            // 特殊值处理规则 (FieldStandardization.drl)
            // 功能：处理全大写、空值等特殊场景
            // 特点：作为数据清洗的基础规则
            "NullAndSpecialValuesRule",

            // 正则模式验证规则 (RegexNormalization.drl)
            // 功能：验证和处理正则表达式模式
            // 特点：自动检测无效正则表达式
            "RegexPatternValidationRule"});

    private final String[] ruleNames;

    RuleGroup(String[] ruleNames) {
        this.ruleNames = ruleNames;
    }

}
