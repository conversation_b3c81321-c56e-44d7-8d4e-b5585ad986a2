package ai.pricefox.mallfox.domain.standard.mongo;

import ai.pricefox.mallfox.common.util.IdGenerator;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.concurrent.atomic.AtomicLong;

/**
 * MongoDB实体类，表示原始商品数据。
 * 包含商品的基本信息和标准SPU/SKU生成逻辑。
 */
@Data
@Document(collection = "raw_data")
@CompoundIndexes({
        // 1. 平台+渠道+标准SPU 的唯一索引
        @CompoundIndex(
                name = "idx_platform_channel_standardSpu",
                def = "{'productPlatform': 1, 'dataChannel': 1, 'standardSpu': 1}",
                unique = true
        ),
        // 2. 平台+渠道+标准SKU 的唯一索引
        @CompoundIndex(
                name = "idx_platform_channel_standardSku",
                def = "{'productPlatform': 1, 'dataChannel': 1, 'standardSku': 1}",
                unique = true
        ),
        // 3. 平台+渠道+原始SKU 的唯一索引
        @CompoundIndex(
                name = "idx_platform_channel_sku",
                def = "{'productPlatform': 1, 'dataChannel': 1, 'sku': 1}",
                unique = true
        ),
        // 4. 平台+渠道+商品标识符 的唯一索引
        @CompoundIndex(
                name = "idx_platform_channel_productIdentifier",
                def = "{'productPlatform': 1, 'dataChannel': 1, 'productIdentifier': 1}",
                unique = true
        )
})
public class RawData {

    @Id
    private String id;
    /**
     * 平台
     */
    private String productPlatform;
    /**
     * 渠道
     */
    private String dataChannel;
    /**
     * 标准SPU编码。
     * 生成规则：10位字符串，格式为 PP + 8位递增数字。
     */
    private String standardSpu;
    /**
     * 标准SKU编码。
     * 生成规则：10位字符串，格式为 PS + 8位递增数字。
     */
    private String standardSku;
    /**
     * spu
     */
    private String spu;
    /**
     * sku
     */
    private String sku;
    /**
     * 商品标识符
     */
    private String productIdentifier;
    /**
     * 商品数据
     */
    private String dataJson;
    /**
     * 创建时间
     */
    private Date createTime;
}