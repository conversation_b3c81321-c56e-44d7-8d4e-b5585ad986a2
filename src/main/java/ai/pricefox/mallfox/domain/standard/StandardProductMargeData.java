package ai.pricefox.mallfox.domain.standard;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR> 商品合并数据
 */
@Data
@TableName(value = "standard_product_attribute_data")
public class StandardProductMargeData implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createDate;

    /**
     * 创建人
     */
    private String createUsername;

    /**
     * 更新时间
     */
    private LocalDateTime updateDate;

    /**
     * 更新人
     */
    private String updateUsername;

    /**
     * 来源平台名称
     */
    private String sourcePlatformName;

    /**
     * 来源平台code
     */
    private String sourcePlatformCode;

    /**
     * 数据渠道 API   爬虫
     */
    private String dataChannel;

    /**
     * 自建spu code
     */
    private String spuCode;

    /**
     * 自建sku code
     */
    private String skuCode;

    /**
     * 第三方平台的spuid
     */
    private String platformSpuId;

    /**
     * 第三放平台skuId
     */
    private String platformSkuId;

    /**
     * 步骤
     */
    private String step;

    /**
     * 审核状态
     */
    private String examineStatus;

    /**
     * 一级类目
     */
    private String categoryLevel1;

    /**
     * 二级类目
     */
    private String categoryLevel2;

    /**
     * 三级类目
     */
    private String categoryLevel3;

    /**
     * 三级类目code
     */
    private String categoryLeve3Code;

    private static final long serialVersionUID = 1L;
}