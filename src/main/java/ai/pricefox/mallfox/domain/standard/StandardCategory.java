package ai.pricefox.mallfox.domain.standard;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 标准品类库
 */
@TableName(value ="standard_category")
@Data
public class StandardCategory implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createDate;

    /**
     * 修改时间
     */
    private LocalDateTime updateDate;

    /**
     * 创建人名称
     */
    private String createUsername;

    /**
     * 更新人名称
     */
    private String updateUsername;

    /**
     * 品类编码
     */
    private String categoryCode;

    /**
     * 品类中文名称
     */
    private String categoryNameCn;

    /**
     * 品类英文名称
     */
    private String categoryNameEn;

    /**
     * 级别:1\2\3
     */
    private Integer level;

    /**
     * 分类图标
     */
    private String iconUrl;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否激活（激活用户端可显示）1：激活 0：不激活
     */
    private Boolean isActive;

    /**
     * 父级ID
     */
    private Long parent;
}